import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from database import DatabaseManager
from datetime import datetime, date
import os

class LoginDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("تسجيل الدخول - Flex USA")
        self.setFixedSize(450, 550)
        self.setWindowFlags(Qt.Dialog | Qt.MSWindowsFixedSizeDialogHint)

        # إعداد الخلفية والستايل العام
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border-radius: 10px;
            }
        """)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء الأقسام
        self.create_header_section(main_layout)
        self.create_form_section(main_layout)
        self.create_footer_section(main_layout)

        self.setLayout(main_layout)

        # ربط الأحداث
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.password_input.returnPressed.connect(self.handle_login)
        self.username_input.textChanged.connect(self.validate_inputs)
        self.password_input.textChanged.connect(self.validate_inputs)

        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()

        # تأثيرات بصرية
        self.add_visual_effects()

        # حالة الزر الأولية
        self.login_button.setEnabled(False)

    def validate_inputs(self):
        """التحقق من صحة المدخلات وتفعيل/إلغاء تفعيل زر الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if username and password:
            self.login_button.setEnabled(True)
            self.login_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #27ae60, stop:1 #229954);
                    color: white;
                    border: none;
                    border-radius: 22px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 10px 20px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2ecc71, stop:1 #27ae60);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #229954, stop:1 #1e8449);
                }
            """)
        else:
            self.login_button.setEnabled(False)
            self.login_button.setStyleSheet("""
                QPushButton {
                    background: #bdc3c7;
                    color: #7f8c8d;
                    border: none;
                    border-radius: 22px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 10px 20px;
                }
            """)

    def handle_login(self):
        """معالجة عملية تسجيل الدخول مع تأثيرات بصرية"""
        if not self.username_input.text().strip() or not self.password_input.text().strip():
            self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # تأثير التحميل
        self.login_button.setText("جاري التحقق...")
        self.login_button.setEnabled(False)

        # محاكاة تأخير التحقق (يمكن إزالته في الإنتاج)
        QApplication.processEvents()

        # استعادة النص الأصلي
        self.login_button.setText("دخول")
        self.login_button.setEnabled(True)

        self.accept()

    def show_error_message(self, message):
        """عرض رسالة خطأ مع تأثيرات بصرية"""
        # إنشاء تسمية خطأ مؤقتة
        if hasattr(self, 'error_label'):
            self.error_label.deleteLater()

        self.error_label = QLabel(message)
        self.error_label.setAlignment(Qt.AlignCenter)
        self.error_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                background: #fadbd8;
                border: 1px solid #e74c3c;
                border-radius: 5px;
                padding: 8px;
                font-size: 12px;
                margin: 5px;
            }
        """)

        # إضافة التسمية إلى التخطيط
        form_widget = self.findChild(QWidget)
        if form_widget:
            layout = form_widget.layout()
            layout.insertWidget(2, self.error_label)

        # إزالة الرسالة بعد 3 ثوان
        QTimer.singleShot(3000, lambda: self.error_label.deleteLater() if hasattr(self, 'error_label') else None)

    def create_header_section(self, main_layout):
        """إنشاء قسم الرأس مع الشعار والعنوان"""
        header_widget = QWidget()
        header_widget.setFixedHeight(180)
        header_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
            }
        """)

        header_layout = QVBoxLayout()
        header_layout.setContentsMargins(20, 20, 20, 20)

        # شعار النظام
        logo_label = QLabel("💰")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: white;
                background: transparent;
                margin: 10px;
            }
        """)
        header_layout.addWidget(logo_label)

        # عنوان النظام
        title_label = QLabel("نظام Flex USA المحاسبي")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: bold;
                color: white;
                background: transparent;
                margin: 5px;
            }
        """)
        header_layout.addWidget(title_label)

        # نص فرعي
        subtitle_label = QLabel("نظام إدارة العمليات المالية")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #ecf0f1;
                background: transparent;
                margin: 5px;
            }
        """)
        header_layout.addWidget(subtitle_label)

        header_widget.setLayout(header_layout)
        main_layout.addWidget(header_widget)

    def create_form_section(self, main_layout):
        """إنشاء قسم النموذج"""
        form_widget = QWidget()
        form_widget.setStyleSheet("""
            QWidget {
                background: white;
                border: none;
            }
        """)

        form_layout = QVBoxLayout()
        form_layout.setContentsMargins(40, 40, 40, 20)
        form_layout.setSpacing(20)

        # عنوان تسجيل الدخول
        login_title = QLabel("تسجيل الدخول")
        login_title.setAlignment(Qt.AlignCenter)
        login_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        form_layout.addWidget(login_title)

        # حقل اسم المستخدم
        username_container = self.create_input_field("👤", "اسم المستخدم")
        self.username_input = username_container.findChild(QLineEdit)
        form_layout.addWidget(username_container)

        # حقل كلمة المرور
        password_container = self.create_input_field("🔒", "كلمة المرور", is_password=True)
        self.password_input = password_container.findChild(QLineEdit)
        form_layout.addWidget(password_container)

        # خيار تذكر كلمة المرور
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("تذكر كلمة المرور")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: #7f8c8d;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                border-radius: 3px;
                background: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #3498db;
                border-radius: 3px;
                background: #3498db;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
        """)
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        form_layout.addLayout(remember_layout)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(45)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background: #95a5a6;
                color: white;
                border: none;
                border-radius: 22px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background: #7f8c8d;
            }
            QPushButton:pressed {
                background: #6c7b7d;
            }
        """)

        self.login_button = QPushButton("دخول")
        self.login_button.setFixedHeight(45)
        self.login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 22px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #1f618d);
            }
        """)

        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        form_layout.addLayout(buttons_layout)

        form_widget.setLayout(form_layout)
        main_layout.addWidget(form_widget)

    def create_input_field(self, icon, placeholder, is_password=False):
        """إنشاء حقل إدخال مع أيقونة"""
        container = QWidget()
        container.setFixedHeight(50)

        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # أيقونة
        icon_label = QLabel(icon)
        icon_label.setFixedSize(50, 50)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                background: #ecf0f1;
                border: 2px solid #bdc3c7;
                border-right: none;
                border-top-left-radius: 25px;
                border-bottom-left-radius: 25px;
                font-size: 16px;
                color: #7f8c8d;
            }
        """)

        # حقل الإدخال
        input_field = QLineEdit()
        input_field.setPlaceholderText(placeholder)
        input_field.setFixedHeight(50)

        if is_password:
            input_field.setEchoMode(QLineEdit.Password)

        input_field.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-left: none;
                border-top-right-radius: 25px;
                border-bottom-right-radius: 25px;
                padding: 0 15px;
                font-size: 14px;
                background: white;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border-color: #3498db;
                outline: none;
            }
            QLineEdit:focus + QLabel {
                border-color: #3498db;
            }
        """)

        layout.addWidget(icon_label)
        layout.addWidget(input_field)

        container.setLayout(layout)
        return container

    def create_footer_section(self, main_layout):
        """إنشاء قسم التذييل"""
        footer_widget = QWidget()
        footer_widget.setFixedHeight(80)
        footer_widget.setStyleSheet("""
            QWidget {
                background: #ecf0f1;
                border-bottom-left-radius: 10px;
                border-bottom-right-radius: 10px;
            }
        """)

        footer_layout = QVBoxLayout()
        footer_layout.setContentsMargins(20, 15, 20, 15)

        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                background: transparent;
            }
        """)
        footer_layout.addWidget(version_label)

        # حقوق الطبع
        copyright_label = QLabel("© 2024 Flex USA - جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 10px;
                background: transparent;
            }
        """)
        footer_layout.addWidget(copyright_label)

        footer_widget.setLayout(footer_layout)
        main_layout.addWidget(footer_widget)

    def add_visual_effects(self):
        """إضافة تأثيرات بصرية"""
        # تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(10)
        shadow.setColor(QColor(0, 0, 0, 80))
        self.setGraphicsEffect(shadow)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.db = DatabaseManager()
        self.current_user = None
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("نظام Flex USA المحاسبي")
        self.setGeometry(100, 100, 1200, 800)

        # تطبيق الأنماط العامة للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: #f8f9fa;
                color: #2c3e50;
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                padding: 5px;
                font-weight: bold;
            }
            QMenuBar::item {
                background: transparent;
                padding: 8px 12px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background: rgba(255,255,255,0.2);
            }
            QMenu {
                background: white;
                color: #2c3e50;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
            }
            QMenu::item:selected {
                background: #667eea;
                color: white;
            }
            QStatusBar {
                background: white;
                color: #2c3e50;
                border-top: 1px solid #e0e0e0;
                font-weight: bold;
            }
        """)

        # إنشاء القائمة الرئيسية
        self.create_menu_bar()

        # إنشاء شريط الأدوات
        self.create_toolbar()

        # إنشاء الواجهة الرئيسية
        self.create_main_interface()

        # إنشاء شريط الحالة
        self.statusBar().showMessage("مرحباً بك في نظام Flex USA المحاسبي")
        
    def create_menu_bar(self):
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        new_transaction_action = QAction('عملية جديدة', self)
        new_transaction_action.triggered.connect(self.add_new_transaction)
        file_menu.addAction(new_transaction_action)
        
        file_menu.addSeparator()
        
        backup_action = QAction('نسخة احتياطية', self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu('التقارير')
        
        daily_report_action = QAction('تقرير يومي', self)
        daily_report_action.triggered.connect(self.generate_daily_report)
        reports_menu.addAction(daily_report_action)
        
        monthly_report_action = QAction('تقرير شهري', self)
        monthly_report_action.triggered.connect(self.generate_monthly_report)
        reports_menu.addAction(monthly_report_action)
        
        # قائمة الإعدادات
        settings_menu = menubar.addMenu('إعدادات')
        
        exchange_rate_action = QAction('أسعار الصرف', self)
        exchange_rate_action.triggered.connect(self.manage_exchange_rates)
        settings_menu.addAction(exchange_rate_action)
        
    def create_toolbar(self):
        toolbar = self.addToolBar('الأدوات الرئيسية')
        toolbar.setStyleSheet("""
            QToolBar {
                background: white;
                border: none;
                border-bottom: 2px solid #667eea;
                padding: 8px;
                spacing: 5px;
            }
            QToolBar QToolButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 8px;
                font-weight: bold;
                margin: 2px;
            }
            QToolBar QToolButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7c8df0, stop:1 #8a5fb8);
            }
            QToolBar QToolButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a6fd8, stop:1 #6b4190);
            }
        """)

        # زر إضافة عملية جديدة
        new_transaction_btn = QAction('➕ عملية جديدة', self)
        new_transaction_btn.triggered.connect(self.add_new_transaction)
        toolbar.addAction(new_transaction_btn)

        toolbar.addSeparator()

        # زر تحديث البيانات
        refresh_btn = QAction('🔄 تحديث', self)
        refresh_btn.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_btn)

        toolbar.addSeparator()

        # زر التقارير السريع
        quick_report_btn = QAction('📊 تقرير سريع', self)
        quick_report_btn.triggered.connect(self.generate_daily_report)
        toolbar.addAction(quick_report_btn)
        
    def create_main_interface(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # لوحة المعلومات الرئيسية
        self.create_dashboard()
        layout.addWidget(self.dashboard_widget)
        
        # جدول العمليات
        self.create_transactions_table()
        layout.addWidget(self.transactions_table)
        
        central_widget.setLayout(layout)
        
    def create_dashboard(self):
        self.dashboard_widget = QGroupBox("لوحة التحكم الرئيسية")
        layout = QHBoxLayout()
        
        # بطاقة الدينار الليبي
        lyd_card = self.create_currency_card("دينار ليبي", "LYD")
        layout.addWidget(lyd_card)
        
        # بطاقة الدولار الأمريكي
        usd_card = self.create_currency_card("دولار أمريكي", "USD")
        layout.addWidget(usd_card)
        
        self.dashboard_widget.setLayout(layout)
        
    def create_currency_card(self, currency_name, currency_code):
        card = QGroupBox(currency_name)
        card.setStyleSheet("""
            QGroupBox {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 15px;
                margin: 10px;
                padding: 20px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px;
                color: #667eea;
                font-size: 18px;
                font-weight: bold;
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(15)

        # أيقونة العملة
        icon = "💵" if currency_code == "USD" else "🏛️"
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px; margin: 10px;")
        layout.addWidget(icon_label)

        # المبالغ المستلمة
        received_label = QLabel("المستلم: 0.00")
        received_label.setStyleSheet("""
            color: #27ae60;
            font-weight: bold;
            font-size: 14px;
            background: #d5f4e6;
            padding: 8px;
            border-radius: 5px;
        """)
        layout.addWidget(received_label)

        # المبالغ المسلمة
        paid_label = QLabel("المسلم: 0.00")
        paid_label.setStyleSheet("""
            color: #e74c3c;
            font-weight: bold;
            font-size: 14px;
            background: #fadbd8;
            padding: 8px;
            border-radius: 5px;
        """)
        layout.addWidget(paid_label)

        # الرصيد
        balance_label = QLabel("الرصيد: 0.00")
        balance_label.setStyleSheet("""
            color: #667eea;
            font-weight: bold;
            font-size: 16px;
            background: #e8ecf7;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #667eea;
        """)
        layout.addWidget(balance_label)

        card.setLayout(layout)

        # حفظ المراجع للتحديث لاحقاً
        if currency_code == "LYD":
            self.lyd_received_label = received_label
            self.lyd_paid_label = paid_label
            self.lyd_balance_label = balance_label
        else:
            self.usd_received_label = received_label
            self.usd_paid_label = paid_label
            self.usd_balance_label = balance_label

        return card
        
    def create_transactions_table(self):
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(8)
        self.transactions_table.setHorizontalHeaderLabels([
            "التاريخ", "النوع", "العملة", "المبلغ", "الجهة", "المرجع", "الوصف", "المستخدم"
        ])

        # تطبيق الأنماط على الجدول
        self.transactions_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 10px;
                gridline-color: #f0f0f0;
                selection-background-color: #667eea;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 12px;
                border: none;
                font-size: 13px;
            }
            QTableWidget::item:selected {
                background: #667eea;
                color: white;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
                font-size: 14px;
            }
        """)

        # تفعيل الألوان المتناوبة
        self.transactions_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.transactions_table.horizontalHeader()
        header.setStretchLastSection(True)

        # تعيين عرض الأعمدة
        self.transactions_table.setColumnWidth(0, 100)  # التاريخ
        self.transactions_table.setColumnWidth(1, 80)   # النوع
        self.transactions_table.setColumnWidth(2, 100)  # العملة
        self.transactions_table.setColumnWidth(3, 100)  # المبلغ
        self.transactions_table.setColumnWidth(4, 150)  # الجهة
        self.transactions_table.setColumnWidth(5, 100)  # المرجع
        
    def login(self):
        """عرض نافذة تسجيل الدخول"""
        try:
            from login_dialog import ProfessionalLoginDialog
            login_dialog = ProfessionalLoginDialog()
        except ImportError:
            # استخدام النافذة الأساسية في حالة عدم توفر النافذة المحسنة
            login_dialog = LoginDialog()

        if login_dialog.exec_() == QDialog.Accepted:
            username = login_dialog.username_input.text()
            password = login_dialog.password_input.text()

            user = self.db.authenticate_user(username, password)
            if user:
                self.current_user = {
                    'id': user[0],
                    'username': user[1],
                    'role': user[2]
                }

                # تحديث عنوان النافذة لإظهار المستخدم الحالي
                self.setWindowTitle(f"نظام Flex USA المحاسبي - {user[1]} ({self.get_role_name(user[2])})")

                # تحديث شريط الحالة
                self.statusBar().showMessage(f"مرحباً {user[1]} - تم تسجيل الدخول بنجاح")

                # تحديث البيانات
                self.refresh_data()

                return True
            else:
                # إظهار رسالة خطأ بسيطة وإعادة المحاولة
                QMessageBox.warning(self, "خطأ في تسجيل الدخول",
                                  "اسم المستخدم أو كلمة المرور غير صحيحة\nيرجى المحاولة مرة أخرى")
                return self.login()  # إعادة عرض نافذة تسجيل الدخول
        return False

    def get_role_name(self, role):
        """الحصول على اسم الدور باللغة العربية"""
        roles = {
            'admin': 'مدير',
            'cashier': 'كاشير',
            'data_entry': 'مدخل بيانات'
        }
        return roles.get(role, role)
        
    def refresh_data(self):
        """تحديث البيانات في الواجهة"""
        # تحديث لوحة التحكم
        balance_summary = self.db.get_balance_summary()
        
        # تحديث بيانات الدينار الليبي
        lyd_data = balance_summary['lyd']
        self.lyd_received_label.setText(f"المستلم: {lyd_data['received']:.2f}")
        self.lyd_paid_label.setText(f"المسلم: {lyd_data['paid']:.2f}")
        self.lyd_balance_label.setText(f"الرصيد: {lyd_data['balance']:.2f}")
        
        # تحديث بيانات الدولار الأمريكي
        usd_data = balance_summary['usd']
        self.usd_received_label.setText(f"المستلم: {usd_data['received']:.2f}")
        self.usd_paid_label.setText(f"المسلم: {usd_data['paid']:.2f}")
        self.usd_balance_label.setText(f"الرصيد: {usd_data['balance']:.2f}")
        
        # تحديث جدول العمليات
        self.load_transactions()
        
    def load_transactions(self):
        """تحميل العمليات في الجدول"""
        transactions = self.db.get_transactions()
        self.transactions_table.setRowCount(len(transactions))
        
        for row, transaction in enumerate(transactions):
            self.transactions_table.setItem(row, 0, QTableWidgetItem(str(transaction[5])))  # التاريخ
            self.transactions_table.setItem(row, 1, QTableWidgetItem(transaction[1]))       # النوع
            self.transactions_table.setItem(row, 2, QTableWidgetItem(transaction[2]))       # العملة
            self.transactions_table.setItem(row, 3, QTableWidgetItem(f"{transaction[3]:.2f}"))  # المبلغ
            self.transactions_table.setItem(row, 4, QTableWidgetItem(transaction[6]))       # الجهة
            self.transactions_table.setItem(row, 5, QTableWidgetItem(transaction[7] or ""))  # المرجع
            self.transactions_table.setItem(row, 6, QTableWidgetItem(transaction[8] or ""))  # الوصف
            self.transactions_table.setItem(row, 7, QTableWidgetItem(transaction[11] or "")) # المستخدم
    
    def add_new_transaction(self):
        """إضافة عملية مالية جديدة"""
        try:
            from transaction_dialog import TransactionDialog
            dialog = TransactionDialog(self.db, self.current_user)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()
        except ImportError:
            QMessageBox.information(self, "إضافة عملية", "سيتم إضافة نافذة العمليات قريباً")
    
    def generate_daily_report(self):
        """إنشاء تقرير يومي"""
        try:
            from reports import ReportGenerator
            from PyQt5.QtWidgets import QFileDialog

            report_gen = ReportGenerator(self.db)

            # اختيار نوع التصدير
            format_choice, ok = QInputDialog.getItem(
                self, "نوع التقرير", "اختر نوع التصدير:",
                ["PDF", "Excel"], 0, False
            )

            if ok:
                filename = report_gen.generate_daily_report(export_format=format_choice.lower())
                QMessageBox.information(self, "تم إنشاء التقرير", f"تم حفظ التقرير في:\n{filename}")

        except ImportError:
            QMessageBox.information(self, "تقرير يومي", "سيتم إضافة هذه الميزة قريباً")

    def generate_monthly_report(self):
        """إنشاء تقرير شهري"""
        try:
            from reports import ReportGenerator

            report_gen = ReportGenerator(self.db)

            # اختيار نوع التصدير
            format_choice, ok = QInputDialog.getItem(
                self, "نوع التقرير", "اختر نوع التصدير:",
                ["PDF", "Excel"], 0, False
            )

            if ok:
                filename = report_gen.generate_monthly_report(export_format=format_choice.lower())
                QMessageBox.information(self, "تم إنشاء التقرير", f"تم حفظ التقرير في:\n{filename}")

        except ImportError:
            QMessageBox.information(self, "تقرير شهري", "سيتم إضافة هذه الميزة قريباً")

    def manage_exchange_rates(self):
        """إدارة أسعار الصرف"""
        try:
            from transaction_dialog import ExchangeRateDialog
            dialog = ExchangeRateDialog(self.db)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "أسعار الصرف", "سيتم إضافة هذه الميزة قريباً")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            from backup_manager import BackupManager, BackupDialog
            backup_manager = BackupManager()
            backup_dialog = BackupDialog(self, backup_manager)
            backup_dialog.show_backup_dialog()
        except ImportError:
            QMessageBox.information(self, "نسخة احتياطية", "سيتم إضافة هذه الميزة قريباً")

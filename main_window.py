from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from database import DatabaseManager
from icon_manager import icon_manager
from unified_theme import UnifiedTheme

def create_arabic_message_box(parent, title, text, icon=QMessageBox.Question):
    """إنشاء نافذة رسالة عربية مع أزرار نعم/لا"""
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(text)
    msg_box.setIcon(icon)
    msg_box.setLayoutDirection(Qt.RightToLeft)

    # تطبيق الأيقونة الموحدة
    icon_manager.apply_to_message_box(msg_box)

    # إضافة أزرار عربية
    yes_button = msg_box.addButton("نعم", QMessageBox.YesRole)
    no_button = msg_box.addButton("لا", QMessageBox.NoRole)
    msg_box.setDefaultButton(no_button)

    # تطبيق الأنماط العربية
    msg_box.setStyleSheet("""
        QMessageBox {
            background: white;
            color: #2c3e50;
            font-family: "Arial";
            qproperty-layoutDirection: RightToLeft;
        }
        QMessageBox QPushButton {
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-weight: bold;
            min-width: 80px;
            font-size: 12px;
        }
        QMessageBox QPushButton:hover {
            background: #2980b9;
        }
        QMessageBox QPushButton:pressed {
            background: #21618c;
        }
        QMessageBox QLabel {
            color: #2c3e50;
            font-size: 14px;
            text-align: right;
        }
    """)

    msg_box.exec_()
    return msg_box.clickedButton() == yes_button

def create_arabic_info_box(parent, title, text, icon=QMessageBox.Information):
    """إنشاء نافذة رسالة معلومات عربية"""
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(text)
    msg_box.setIcon(icon)
    msg_box.setLayoutDirection(Qt.RightToLeft)

    # تطبيق الأيقونة الموحدة
    icon_manager.apply_to_message_box(msg_box)

    # إضافة زر موافق عربي
    ok_button = msg_box.addButton("موافق", QMessageBox.AcceptRole)
    msg_box.setDefaultButton(ok_button)

    # تطبيق الأنماط العربية
    if icon == QMessageBox.Information:
        button_color = "#27ae60"
        button_hover = "#2ecc71"
    elif icon == QMessageBox.Warning:
        button_color = "#f39c12"
        button_hover = "#e67e22"
    elif icon == QMessageBox.Critical:
        button_color = "#e74c3c"
        button_hover = "#c0392b"
    else:
        button_color = "#3498db"
        button_hover = "#2980b9"

    msg_box.setStyleSheet(f"""
        QMessageBox {{
            background: white;
            color: #2c3e50;
            font-family: "Arial";
            qproperty-layoutDirection: RightToLeft;
        }}
        QMessageBox QPushButton {{
            background: {button_color};
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-weight: bold;
            min-width: 80px;
            font-size: 12px;
        }}
        QMessageBox QPushButton:hover {{
            background: {button_hover};
        }}
        QMessageBox QLabel {{
            color: #2c3e50;
            font-size: 14px;
            text-align: right;
        }}
    """)

    msg_box.exec_()

class LoginDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔐 تسجيل الدخول - Flex USA")
        self.setFixedSize(450, 550)
        self.setWindowFlags(Qt.Dialog | Qt.MSWindowsFixedSizeDialogHint)

        # إعداد الخلفية والستايل العام
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border-radius: 10px;
            }
        """)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء الأقسام
        self.create_header_section(main_layout)
        self.create_form_section(main_layout)
        self.create_footer_section(main_layout)

        self.setLayout(main_layout)

        # ربط الأحداث
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.password_input.returnPressed.connect(self.handle_login)
        self.username_input.textChanged.connect(self.validate_inputs)
        self.password_input.textChanged.connect(self.validate_inputs)

        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()

        # تأثيرات بصرية
        self.add_visual_effects()

        # حالة الزر الأولية
        self.login_button.setEnabled(False)

    def validate_inputs(self):
        """التحقق من صحة المدخلات وتفعيل/إلغاء تفعيل زر الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if username and password:
            self.login_button.setEnabled(True)
            self.login_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #27ae60, stop:1 #229954);
                    color: white;
                    border: none;
                    border-radius: 22px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 10px 20px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2ecc71, stop:1 #27ae60);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #229954, stop:1 #1e8449);
                }
            """)
        else:
            self.login_button.setEnabled(False)
            self.login_button.setStyleSheet("""
                QPushButton {
                    background: #bdc3c7;
                    color: #7f8c8d;
                    border: none;
                    border-radius: 22px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 10px 20px;
                }
            """)

    def handle_login(self):
        """معالجة عملية تسجيل الدخول مع تأثيرات بصرية"""
        if not self.username_input.text().strip() or not self.password_input.text().strip():
            self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # تأثير التحميل
        self.login_button.setText("جاري التحقق...")
        self.login_button.setEnabled(False)

        # محاكاة تأخير التحقق (يمكن إزالته في الإنتاج)
        QApplication.processEvents()

        # استعادة النص الأصلي
        self.login_button.setText("دخول")
        self.login_button.setEnabled(True)

        self.accept()

    def show_error_message(self, message):
        """عرض رسالة خطأ مع تأثيرات بصرية"""
        # إنشاء تسمية خطأ مؤقتة
        if hasattr(self, 'error_label'):
            self.error_label.deleteLater()

        self.error_label = QLabel(message)
        self.error_label.setAlignment(Qt.AlignCenter)
        self.error_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                background: #fadbd8;
                border: 1px solid #e74c3c;
                border-radius: 5px;
                padding: 8px;
                font-size: 12px;
                margin: 5px;
            }
        """)

        # إضافة التسمية إلى التخطيط
        form_widget = self.findChild(QWidget)
        if form_widget:
            layout = form_widget.layout()
            layout.insertWidget(2, self.error_label)

        # إزالة الرسالة بعد 3 ثوان
        QTimer.singleShot(3000, lambda: self.error_label.deleteLater() if hasattr(self, 'error_label') else None)

    def create_header_section(self, main_layout):
        """إنشاء قسم الرأس مع الشعار والعنوان"""
        header_widget = QWidget()
        header_widget.setFixedHeight(180)
        header_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
            }
        """)

        header_layout = QVBoxLayout()
        header_layout.setContentsMargins(20, 20, 20, 20)

        # شعار النظام
        logo_label = QLabel("💰")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: white;
                background: transparent;
                margin: 10px;
            }
        """)
        header_layout.addWidget(logo_label)

        # عنوان النظام
        title_label = QLabel("نظام Flex USA المحاسبي")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: bold;
                color: white;
                background: transparent;
                margin: 5px;
            }
        """)
        header_layout.addWidget(title_label)

        # نص فرعي
        subtitle_label = QLabel("نظام إدارة العمليات المالية")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #ecf0f1;
                background: transparent;
                margin: 5px;
            }
        """)
        header_layout.addWidget(subtitle_label)

        header_widget.setLayout(header_layout)
        main_layout.addWidget(header_widget)

    def create_form_section(self, main_layout):
        """إنشاء قسم النموذج"""
        form_widget = QWidget()
        form_widget.setStyleSheet("""
            QWidget {
                background: white;
                border: none;
            }
        """)

        form_layout = QVBoxLayout()
        form_layout.setContentsMargins(40, 40, 40, 20)
        form_layout.setSpacing(20)

        # عنوان تسجيل الدخول
        login_title = QLabel("تسجيل الدخول")
        login_title.setAlignment(Qt.AlignCenter)
        login_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        form_layout.addWidget(login_title)

        # حقل اسم المستخدم
        username_container = self.create_input_field("👤", "اسم المستخدم")
        self.username_input = username_container.findChild(QLineEdit)
        form_layout.addWidget(username_container)

        # حقل كلمة المرور
        password_container = self.create_input_field("🔒", "كلمة المرور", is_password=True)
        self.password_input = password_container.findChild(QLineEdit)
        form_layout.addWidget(password_container)

        # خيار تذكر كلمة المرور
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("تذكر كلمة المرور")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: #7f8c8d;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                border-radius: 3px;
                background: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #3498db;
                border-radius: 3px;
                background: #3498db;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
        """)
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        form_layout.addLayout(remember_layout)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFixedHeight(45)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background: #95a5a6;
                color: white;
                border: none;
                border-radius: 22px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background: #7f8c8d;
            }
            QPushButton:pressed {
                background: #6c7b7d;
            }
        """)

        self.login_button = QPushButton("دخول")
        self.login_button.setFixedHeight(45)
        self.login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 22px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #1f618d);
            }
        """)

        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        form_layout.addLayout(buttons_layout)

        form_widget.setLayout(form_layout)
        main_layout.addWidget(form_widget)

    def create_input_field(self, icon, placeholder, is_password=False):
        """إنشاء حقل إدخال مع أيقونة"""
        container = QWidget()
        container.setFixedHeight(50)

        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # أيقونة
        icon_label = QLabel(icon)
        icon_label.setFixedSize(50, 50)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                background: #ecf0f1;
                border: 2px solid #bdc3c7;
                border-right: none;
                border-top-left-radius: 25px;
                border-bottom-left-radius: 25px;
                font-size: 16px;
                color: #7f8c8d;
            }
        """)

        # حقل الإدخال
        input_field = QLineEdit()
        input_field.setPlaceholderText(placeholder)
        input_field.setFixedHeight(50)

        if is_password:
            input_field.setEchoMode(QLineEdit.Password)

        input_field.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-left: none;
                border-top-right-radius: 25px;
                border-bottom-right-radius: 25px;
                padding: 0 15px;
                font-size: 14px;
                background: white;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border-color: #3498db;
                outline: none;
            }
            QLineEdit:focus + QLabel {
                border-color: #3498db;
            }
        """)

        layout.addWidget(icon_label)
        layout.addWidget(input_field)

        container.setLayout(layout)
        return container

    def create_footer_section(self, main_layout):
        """إنشاء قسم التذييل"""
        footer_widget = QWidget()
        footer_widget.setFixedHeight(80)
        footer_widget.setStyleSheet("""
            QWidget {
                background: #ecf0f1;
                border-bottom-left-radius: 10px;
                border-bottom-right-radius: 10px;
            }
        """)

        footer_layout = QVBoxLayout()
        footer_layout.setContentsMargins(20, 15, 20, 15)

        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                background: transparent;
            }
        """)
        footer_layout.addWidget(version_label)

        # حقوق الطبع
        copyright_label = QLabel("© 2024 Flex USA - جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 10px;
                background: transparent;
            }
        """)
        footer_layout.addWidget(copyright_label)

        footer_widget.setLayout(footer_layout)
        main_layout.addWidget(footer_widget)

    def add_visual_effects(self):
        """إضافة تأثيرات بصرية"""
        # تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(10)
        shadow.setColor(QColor(0, 0, 0, 80))
        self.setGraphicsEffect(shadow)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.db = DatabaseManager()
        self.current_user = None
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("💰 نظام Flex USA المحاسبي")
        self.setGeometry(100, 100, 1200, 800)

        # تطبيق الأيقونة الموحدة
        icon_manager.apply_to_window(self)

        # تطبيق النظام الموحد للألوان والأنماط
        self.setStyleSheet(UnifiedTheme.get_main_window_style())

        # إعداد الاتجاه من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق الأنماط العامة للنافذة مع دعم RTL
        self.setStyleSheet("""
            QMainWindow {
                background: #f8f9fa;
                color: #2c3e50;
                qproperty-layoutDirection: RightToLeft;
            }
            * {
                font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
                direction: rtl;
            }
            QLabel {
                text-align: right;
                qproperty-alignment: AlignRight;
            }
            QLineEdit {
                text-align: right;
                qproperty-alignment: AlignRight;
            }
            QTextEdit {
                text-align: right;
                qproperty-alignment: AlignRight;
            }
            QComboBox {
                text-align: right;
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                padding: 5px;
                font-weight: bold;
            }
            QMenuBar::item {
                background: transparent;
                padding: 8px 12px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background: rgba(255,255,255,0.2);
            }
            QMenu {
                background: white;
                color: #2c3e50;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
            }
            QMenu::item:selected {
                background: #667eea;
                color: white;
            }
            QStatusBar {
                background: white;
                color: #2c3e50;
                border-top: 1px solid #e0e0e0;
                font-weight: bold;
            }
        """)

        # إنشاء القائمة الرئيسية
        self.create_menu_bar()

        # إنشاء شريط الأدوات
        self.create_toolbar()

        # إنشاء الواجهة الرئيسية
        self.create_main_interface()

        # إنشاء شريط الحالة
        self.statusBar().showMessage("مرحباً بك في نظام Flex USA المحاسبي")
        
    def create_menu_bar(self):
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu('📁 ملف')

        new_transaction_action = QAction('➕ عملية جديدة', self)
        new_transaction_action.triggered.connect(self.add_new_transaction)
        file_menu.addAction(new_transaction_action)

        file_menu.addSeparator()

        backup_action = QAction('💾 نسخة احتياطية', self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)

        # إدارة المستخدمين والصلاحيات
        users_management_action = QAction('👥 إدارة المستخدمين والصلاحيات', self)
        users_management_action.triggered.connect(self.manage_users_and_permissions)
        file_menu.addAction(users_management_action)

        # إعدادات الشركة
        company_settings_action = QAction('🏢 إعدادات الشركة', self)
        company_settings_action.triggered.connect(self.manage_company_settings)
        file_menu.addAction(company_settings_action)

        file_menu.addSeparator()

        exit_action = QAction('🚪 خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة التقارير
        reports_menu = menubar.addMenu('📊 التقارير')

        daily_report_action = QAction('📅 تقرير يومي', self)
        daily_report_action.triggered.connect(self.generate_daily_report)
        reports_menu.addAction(daily_report_action)

        monthly_report_action = QAction('📈 تقرير شهري', self)
        monthly_report_action.triggered.connect(self.generate_monthly_report)
        reports_menu.addAction(monthly_report_action)

        # قائمة الإعدادات
        settings_menu = menubar.addMenu('⚙️ إعدادات')

        exchange_rate_action = QAction('💱 أسعار الصرف', self)
        exchange_rate_action.triggered.connect(self.manage_exchange_rates)
        settings_menu.addAction(exchange_rate_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu('🆘 المساعدة')

        user_guide_action = QAction('📖 دليل المستخدم', self)
        user_guide_action.triggered.connect(self.show_user_guide)
        help_menu.addAction(user_guide_action)

        help_menu.addSeparator()

        shortcuts_action = QAction('⌨️ اختصارات لوحة المفاتيح', self)
        shortcuts_action.triggered.connect(self.show_keyboard_shortcuts)
        help_menu.addAction(shortcuts_action)

        help_menu.addSeparator()

        about_action = QAction('ℹ️ حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

        # قائمة الخروج
        exit_menu = menubar.addMenu('🚪 الخروج')

        logout_action = QAction('🔓 تسجيل خروج', self)
        logout_action.triggered.connect(self.logout)
        exit_menu.addAction(logout_action)

        exit_menu.addSeparator()

        exit_app_action = QAction('❌ إغلاق البرنامج', self)
        exit_app_action.triggered.connect(self.close)
        exit_menu.addAction(exit_app_action)
        
    def create_toolbar(self):
        toolbar = self.addToolBar('🛠️ الأدوات الرئيسية')
        toolbar.setStyleSheet("""
            QToolBar {
                background: white;
                border: none;
                border-bottom: 2px solid #667eea;
                padding: 8px;
                spacing: 5px;
            }
            QToolBar QToolButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 8px;
                font-weight: bold;
                margin: 2px;
            }
            QToolBar QToolButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7c8df0, stop:1 #8a5fb8);
            }
            QToolBar QToolButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a6fd8, stop:1 #6b4190);
            }
        """)

        # زر إضافة عملية جديدة
        new_transaction_btn = QAction('➕ عملية جديدة', self)
        new_transaction_btn.triggered.connect(self.add_new_transaction)
        toolbar.addAction(new_transaction_btn)

        toolbar.addSeparator()

        # زر تحديث البيانات
        refresh_btn = QAction('🔄 تحديث', self)
        refresh_btn.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_btn)

        toolbar.addSeparator()

        # زر التقارير السريع
        quick_report_btn = QAction('📊 تقرير سريع', self)
        quick_report_btn.triggered.connect(self.generate_daily_report)
        toolbar.addAction(quick_report_btn)

        toolbar.addSeparator()

        # زر النسخة الاحتياطية السريع
        backup_btn = QAction('💾 نسخة احتياطية', self)
        backup_btn.triggered.connect(self.create_backup)
        toolbar.addAction(backup_btn)

        toolbar.addSeparator()

        # زر إدارة المستخدمين السريع
        users_btn = QAction('👥 المستخدمين', self)
        users_btn.triggered.connect(self.manage_users_and_permissions)
        toolbar.addAction(users_btn)

        toolbar.addSeparator()

        # زر المساعدة السريع
        help_btn = QAction('🆘 مساعدة', self)
        help_btn.triggered.connect(self.show_user_guide)
        toolbar.addAction(help_btn)
        
    def create_main_interface(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # لوحة المعلومات الرئيسية
        self.create_dashboard()
        layout.addWidget(self.dashboard_widget)
        
        # جدول العمليات
        self.create_transactions_table()
        layout.addWidget(self.transactions_table)
        
        central_widget.setLayout(layout)
        
    def create_dashboard(self):
        self.dashboard_widget = QGroupBox("لوحة التحكم الرئيسية")
        layout = QHBoxLayout()
        
        # بطاقة الدينار الليبي
        lyd_card = self.create_currency_card("دينار ليبي", "LYD")
        layout.addWidget(lyd_card)
        
        # بطاقة الدولار الأمريكي
        usd_card = self.create_currency_card("دولار أمريكي", "USD")
        layout.addWidget(usd_card)
        
        self.dashboard_widget.setLayout(layout)
        
    def create_currency_card(self, currency_name, currency_code):
        card = QGroupBox(currency_name)
        card.setStyleSheet("""
            QGroupBox {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 15px;
                margin: 10px;
                padding: 20px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px;
                color: #667eea;
                font-size: 18px;
                font-weight: bold;
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(15)

        # أيقونة العملة
        icon = "💵" if currency_code == "USD" else "🏛️"
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px; margin: 10px;")
        layout.addWidget(icon_label)

        # المبالغ المستلمة
        received_label = QLabel("المستلم: 0.00")
        received_label.setStyleSheet("""
            color: #27ae60;
            font-weight: bold;
            font-size: 14px;
            background: #d5f4e6;
            padding: 8px;
            border-radius: 5px;
        """)
        layout.addWidget(received_label)

        # المبالغ المسلمة
        paid_label = QLabel("المسلم: 0.00")
        paid_label.setStyleSheet("""
            color: #e74c3c;
            font-weight: bold;
            font-size: 14px;
            background: #fadbd8;
            padding: 8px;
            border-radius: 5px;
        """)
        layout.addWidget(paid_label)

        # الرصيد
        balance_label = QLabel("الرصيد: 0.00")
        balance_label.setStyleSheet("""
            color: #667eea;
            font-weight: bold;
            font-size: 16px;
            background: #e8ecf7;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #667eea;
        """)
        layout.addWidget(balance_label)

        card.setLayout(layout)

        # حفظ المراجع للتحديث لاحقاً
        if currency_code == "LYD":
            self.lyd_received_label = received_label
            self.lyd_paid_label = paid_label
            self.lyd_balance_label = balance_label
        else:
            self.usd_received_label = received_label
            self.usd_paid_label = paid_label
            self.usd_balance_label = balance_label

        return card
        
    def create_transactions_table(self):
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(9)
        self.transactions_table.setHorizontalHeaderLabels([
            "التاريخ", "النوع", "العملة", "المبلغ", "الجهة", "المرجع", "الوصف", "المستخدم", "الإجراءات"
        ])

        # تطبيق النظام الموحد على الجدول مع دعم RTL
        table_style = UnifiedTheme.get_table_style() + """
            QTableWidget {
                qproperty-layoutDirection: RightToLeft;
                font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
            }
            QTableWidget::item {
                text-align: right;
                padding-right: 15px;
                min-height: 30px;
            }
            QHeaderView::section {
                text-align: center;
                min-height: 40px;
                border-right: 1px solid rgba(0, 0, 0, 0.1);
            }
            QHeaderView::section:last {
                border-right: none;
            }
        """
        self.transactions_table.setStyleSheet(table_style)

        # تفعيل الألوان المتناوبة
        self.transactions_table.setAlternatingRowColors(True)

        # تعيين سلوك التحديد
        self.transactions_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        # تعيين ارتفاع الصفوف لتتناسب مع الأزرار (28px + تباعد)
        self.transactions_table.verticalHeader().setDefaultSectionSize(38)
        self.transactions_table.verticalHeader().setMinimumSectionSize(35)
        self.transactions_table.verticalHeader().hide()  # إخفاء أرقام الصفوف

        # تعديل عرض الأعمدة
        header = self.transactions_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setDefaultSectionSize(45)
        header.setMinimumSectionSize(40)

        # تعيين عرض الأعمدة
        self.transactions_table.setColumnWidth(0, 100)  # التاريخ
        self.transactions_table.setColumnWidth(1, 80)   # النوع
        self.transactions_table.setColumnWidth(2, 100)  # العملة
        self.transactions_table.setColumnWidth(3, 100)  # المبلغ
        self.transactions_table.setColumnWidth(4, 150)  # الجهة
        self.transactions_table.setColumnWidth(5, 100)  # المرجع
        self.transactions_table.setColumnWidth(6, 120)  # الوصف
        self.transactions_table.setColumnWidth(7, 100)  # المستخدم
        self.transactions_table.setColumnWidth(8, 120)  # الإجراءات
        
    def login(self):
        """عرض نافذة تسجيل الدخول"""
        try:
            from login_dialog import ProfessionalLoginDialog
            login_dialog = ProfessionalLoginDialog()
        except ImportError:
            # استخدام النافذة الأساسية في حالة عدم توفر النافذة المحسنة
            login_dialog = LoginDialog()

        if login_dialog.exec_() == QDialog.Accepted:
            username = login_dialog.username_input.text()
            password = login_dialog.password_input.text()

            user = self.db.authenticate_user(username, password)
            if user:
                self.current_user = {
                    'id': user[0],
                    'username': user[1],
                    'role': user[2]
                }

                # تحديث عنوان النافذة لإظهار المستخدم الحالي
                self.setWindowTitle(f"نظام Flex USA المحاسبي - {user[1]} ({self.get_role_name(user[2])})")

                # تحديث شريط الحالة
                self.statusBar().showMessage(f"مرحباً {user[1]} - تم تسجيل الدخول بنجاح")

                # تحديث البيانات
                self.refresh_data()

                return True
            else:
                # إظهار رسالة خطأ بسيطة وإعادة المحاولة
                create_arabic_info_box(self, "⚠️ خطأ في تسجيل الدخول",
                                     "اسم المستخدم أو كلمة المرور غير صحيحة\nيرجى المحاولة مرة أخرى",
                                     QMessageBox.Warning)
                return self.login()  # إعادة عرض نافذة تسجيل الدخول
        return False

    def get_role_name(self, role):
        """الحصول على اسم الدور باللغة العربية"""
        roles = {
            'admin': 'مدير',
            'cashier': 'كاشير',
            'data_entry': 'مدخل بيانات'
        }
        return roles.get(role, role)
        
    def refresh_data(self):
        """تحديث البيانات في الواجهة"""
        # تحديث لوحة التحكم
        balance_summary = self.db.get_balance_summary()
        
        # تحديث بيانات الدينار الليبي
        lyd_data = balance_summary['lyd']
        self.lyd_received_label.setText(f"المستلم: {lyd_data['received']:.2f}")
        self.lyd_paid_label.setText(f"المسلم: {lyd_data['paid']:.2f}")
        self.lyd_balance_label.setText(f"الرصيد: {lyd_data['balance']:.2f}")
        
        # تحديث بيانات الدولار الأمريكي
        usd_data = balance_summary['usd']
        self.usd_received_label.setText(f"المستلم: {usd_data['received']:.2f}")
        self.usd_paid_label.setText(f"المسلم: {usd_data['paid']:.2f}")
        self.usd_balance_label.setText(f"الرصيد: {usd_data['balance']:.2f}")
        
        # تحديث جدول العمليات
        self.load_transactions()
        
    def load_transactions(self):
        """تحميل العمليات في الجدول"""
        transactions = self.db.get_transactions()
        self.transactions_table.setRowCount(len(transactions))

        for row, transaction in enumerate(transactions):
            self.transactions_table.setItem(row, 0, QTableWidgetItem(str(transaction[5])))  # التاريخ
            self.transactions_table.setItem(row, 1, QTableWidgetItem(transaction[1]))       # النوع
            self.transactions_table.setItem(row, 2, QTableWidgetItem(transaction[2]))       # العملة
            self.transactions_table.setItem(row, 3, QTableWidgetItem(f"{transaction[3]:.2f}"))  # المبلغ
            self.transactions_table.setItem(row, 4, QTableWidgetItem(transaction[6]))       # الجهة
            self.transactions_table.setItem(row, 5, QTableWidgetItem(transaction[7] or ""))  # المرجع
            self.transactions_table.setItem(row, 6, QTableWidgetItem(transaction[8] or ""))  # الوصف
            self.transactions_table.setItem(row, 7, QTableWidgetItem(transaction[11] or "")) # المستخدم

            # إضافة أزرار الإجراءات
            actions_widget = self.create_action_buttons(transaction[0])  # تمرير معرف العملية
            self.transactions_table.setCellWidget(row, 8, actions_widget)

            # تعيين ارتفاع الصف لهذا الصف تحديداً (مناسب للأزرار 28px)
            self.transactions_table.setRowHeight(row, 38)

    def create_action_buttons(self, transaction_id):
        """إنشاء أزرار الإجراءات المحسنة للعملية"""
        # إنشاء الحاوية الرئيسية
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
            }
        """)

        # تخطيط أفقي مع تباعد محسن للارتفاع الجديد
        layout = QHBoxLayout()
        layout.setContentsMargins(6, 3, 6, 3)
        layout.setSpacing(6)
        layout.setAlignment(Qt.AlignCenter)

        # زر التعديل المحسن
        edit_btn = QPushButton()
        edit_btn.setText("✏️")
        edit_btn.setToolTip("تعديل العملية المالية")
        edit_btn.setFixedSize(32, 28)
        edit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedTheme.WARNING_COLOR}, stop:1 #D4A574);
                color: white;
                border: 1px solid #d68910;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                padding: 2px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e67e22, stop:1 #d68910);
                border: 1px solid #b7950b;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d68910, stop:1 #b7950b);
                border: 1px solid #9c7c0a;
            }
        """)
        edit_btn.clicked.connect(lambda: self.edit_transaction(transaction_id))

        # زر الحذف المحسن
        delete_btn = QPushButton()
        delete_btn.setText("🗑️")
        delete_btn.setToolTip("حذف العملية المالية")
        delete_btn.setFixedSize(32, 28)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedTheme.ERROR_COLOR}, stop:1 #C44536);
                color: white;
                border: 1px solid #a93226;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                padding: 2px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
                border: 1px solid #922b21;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #a93226, stop:1 #922b21);
                border: 1px solid #7b241c;
            }
        """)
        delete_btn.clicked.connect(lambda: self.delete_transaction(transaction_id))

        # زر طباعة الفاتورة الجديد
        print_btn = QPushButton()
        print_btn.setText("🖨️")
        print_btn.setToolTip("طباعة فاتورة العملية")
        print_btn.setFixedSize(32, 28)
        print_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedTheme.SECONDARY_COLOR}, stop:1 {UnifiedTheme.PRIMARY_COLOR});
                color: white;
                border: 1px solid #7d3c98;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                padding: 2px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #8e44ad, stop:1 #7d3c98);
                border: 1px solid #6c3483;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7d3c98, stop:1 #6c3483);
                border: 1px solid #5b2c6f;
            }
        """)
        print_btn.clicked.connect(lambda: self.print_invoice(transaction_id))

        # إضافة الأزرار للتخطيط
        layout.addWidget(print_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)

        widget.setLayout(layout)
        return widget

    def print_invoice(self, transaction_id):
        """طباعة فاتورة العملية المالية"""
        try:
            # التحقق من وجود العملية
            transaction = self.db.get_transaction_by_id(transaction_id)
            if not transaction:
                create_arabic_info_box(self, "❌ خطأ", "لم يتم العثور على العملية المطلوبة", QMessageBox.Critical)
                return

            # استيراد طابعة الفواتير
            from invoice_printer import InvoicePrinter

            # إنشاء طابعة الفواتير
            printer = InvoicePrinter(self.db)

            # طباعة الفاتورة
            invoice_path = printer.print_invoice(transaction_id)

            # رسالة نجاح مع خيار فتح الملف
            reply = create_arabic_message_box(
                self,
                "🖨️ تم إنشاء الفاتورة",
                f"تم إنشاء الفاتورة بنجاح!\n\nمسار الملف:\n{invoice_path}\n\nهل تريد فتح الفاتورة الآن؟"
            )

            if reply:
                # فتح الفاتورة
                try:
                    import subprocess
                    import platform

                    if platform.system() == 'Windows':
                        subprocess.run(['start', invoice_path], shell=True, check=True)
                    elif platform.system() == 'Darwin':  # macOS
                        subprocess.run(['open', invoice_path], check=True)
                    else:  # Linux
                        subprocess.run(['xdg-open', invoice_path], check=True)

                except Exception:
                    create_arabic_info_box(
                        self,
                        "⚠️ تحذير",
                        f"تم إنشاء الفاتورة بنجاح ولكن لم يتم فتحها تلقائياً.\nيمكنك العثور عليها في:\n{invoice_path}",
                        QMessageBox.Warning
                    )

        except Exception as e:
            create_arabic_info_box(self, "❌ خطأ", f"حدث خطأ أثناء طباعة الفاتورة:\n{str(e)}", QMessageBox.Critical)

    def manage_company_settings(self):
        """إدارة إعدادات الشركة"""
        from config import Config

        # إنشاء نافذة الإعدادات
        settings_dialog = QDialog(self)
        settings_dialog.setWindowTitle("🏢 إعدادات الشركة")
        settings_dialog.setFixedSize(600, 700)
        settings_dialog.setLayoutDirection(Qt.RightToLeft)

        # تطبيق الأيقونة الموحدة
        icon_manager.apply_to_window(settings_dialog)

        # تطبيق الأنماط
        settings_dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-radius: 10px;
            }
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                font-size: 12px;
            }
            QLineEdit, QTextEdit {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
            }
            QLineEdit:focus, QTextEdit:focus {
                border: 2px solid #667eea;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #764ba2, stop:1 #667eea);
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel("🏢 إعدادات معلومات الشركة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                padding: 15px;
                border-radius: 8px;
                text-align: center;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # إطار النموذج
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)

        form_layout = QFormLayout()
        form_layout.setSpacing(12)

        # الحصول على الإعدادات الحالية
        company_info = Config.get_company_info()

        # حقول الإدخال
        fields = {}

        # اسم الشركة بالعربية
        fields['name'] = QLineEdit(company_info.get('name', ''))
        fields['name'].setPlaceholderText("اسم الشركة بالعربية")
        form_layout.addRow("🏢 اسم الشركة:", fields['name'])

        # اسم الشركة بالإنجليزية
        fields['name_en'] = QLineEdit(company_info.get('name_en', ''))
        fields['name_en'].setPlaceholderText("Company Name in English")
        form_layout.addRow("🏢 الاسم بالإنجليزية:", fields['name_en'])

        # العنوان بالعربية
        fields['address'] = QTextEdit(company_info.get('address', ''))
        fields['address'].setMaximumHeight(60)
        fields['address'].setPlaceholderText("العنوان بالعربية")
        form_layout.addRow("📍 العنوان:", fields['address'])

        # العنوان بالإنجليزية
        fields['address_en'] = QTextEdit(company_info.get('address_en', ''))
        fields['address_en'].setMaximumHeight(60)
        fields['address_en'].setPlaceholderText("Address in English")
        form_layout.addRow("📍 العنوان بالإنجليزية:", fields['address_en'])

        # رقم الهاتف
        fields['phone'] = QLineEdit(company_info.get('phone', ''))
        fields['phone'].setPlaceholderText("+218-21-123-4567")
        form_layout.addRow("📞 رقم الهاتف:", fields['phone'])

        # رقم الجوال
        fields['mobile'] = QLineEdit(company_info.get('mobile', ''))
        fields['mobile'].setPlaceholderText("+218-91-234-5678")
        form_layout.addRow("📱 رقم الجوال:", fields['mobile'])

        # البريد الإلكتروني
        fields['email'] = QLineEdit(company_info.get('email', ''))
        fields['email'].setPlaceholderText("<EMAIL>")
        form_layout.addRow("✉️ البريد الإلكتروني:", fields['email'])

        # الموقع الإلكتروني
        fields['website'] = QLineEdit(company_info.get('website', ''))
        fields['website'].setPlaceholderText("www.company.com")
        form_layout.addRow("🌐 الموقع الإلكتروني:", fields['website'])

        # رقم الترخيص
        fields['license_no'] = QLineEdit(company_info.get('license_no', ''))
        fields['license_no'].setPlaceholderText("LIC-2024-001")
        form_layout.addRow("📜 رقم الترخيص:", fields['license_no'])

        # الرقم الضريبي
        fields['tax_no'] = QLineEdit(company_info.get('tax_no', ''))
        fields['tax_no'].setPlaceholderText("TAX-*********")
        form_layout.addRow("🧾 الرقم الضريبي:", fields['tax_no'])

        form_frame.setLayout(form_layout)
        layout.addWidget(form_frame)

        # أزرار التحكم
        button_layout = QHBoxLayout()

        save_btn = QPushButton("💾 حفظ الإعدادات")
        save_btn.clicked.connect(lambda: self.save_company_settings(fields, settings_dialog))

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
        """)
        cancel_btn.clicked.connect(settings_dialog.reject)

        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        settings_dialog.setLayout(layout)
        settings_dialog.exec_()

    def save_company_settings(self, fields, dialog):
        """حفظ إعدادات الشركة"""
        try:
            from config import Config

            # جمع البيانات من الحقول
            company_info = {}
            for key, field in fields.items():
                if isinstance(field, QTextEdit):
                    company_info[key] = field.toPlainText().strip()
                else:
                    company_info[key] = field.text().strip()

            # حفظ الإعدادات
            if Config.save_company_info(company_info):
                create_arabic_info_box(dialog, "✅ تم الحفظ", "تم حفظ إعدادات الشركة بنجاح")
                dialog.accept()
            else:
                create_arabic_info_box(dialog, "❌ خطأ", "فشل في حفظ إعدادات الشركة", QMessageBox.Critical)

        except Exception as e:
            create_arabic_info_box(dialog, "❌ خطأ", f"حدث خطأ أثناء حفظ الإعدادات:\n{str(e)}", QMessageBox.Critical)

    def edit_transaction(self, transaction_id):
        """تعديل عملية مالية"""
        try:
            from transaction_dialog import EditTransactionDialog
            dialog = EditTransactionDialog(self.db, self.current_user, transaction_id)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()
        except ImportError:
            create_arabic_info_box(self, "✏️ تعديل العملية", "سيتم إضافة هذه الميزة قريباً")

    def delete_transaction(self, transaction_id):
        """حذف عملية مالية"""
        # الحصول على بيانات العملية للتأكيد
        transaction = self.db.get_transaction_by_id(transaction_id)
        if not transaction:
            create_arabic_info_box(self, "❌ خطأ", "لم يتم العثور على العملية المطلوبة", QMessageBox.Critical)
            return

        # رسالة تأكيد الحذف
        transaction_info = f"""
العملية: {transaction[1]} - {transaction[2]}
المبلغ: {transaction[3]:.2f}
الجهة: {transaction[6]}
المرجع: {transaction[7] or 'غير محدد'}
التاريخ: {transaction[5]}
        """

        if create_arabic_message_box(
            self,
            "🗑️ تأكيد حذف العملية",
            f"هل أنت متأكد من حذف هذه العملية؟\n{transaction_info}\n⚠️ تحذير: لا يمكن التراجع عن هذا الإجراء!"
        ):
            try:
                self.db.delete_transaction(transaction_id)
                create_arabic_info_box(self, "✅ تم الحذف", "تم حذف العملية المالية بنجاح")
                self.refresh_data()
            except Exception as e:
                create_arabic_info_box(self, "❌ خطأ", f"حدث خطأ أثناء حذف العملية:\n{str(e)}", QMessageBox.Critical)
    
    def add_new_transaction(self):
        """إضافة عملية مالية جديدة"""
        try:
            from transaction_dialog import TransactionDialog
            dialog = TransactionDialog(self.db, self.current_user)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()
        except ImportError:
            create_arabic_info_box(self, "💰 إضافة عملية", "سيتم إضافة نافذة العمليات قريباً")
    
    def generate_daily_report(self):
        """إنشاء تقرير يومي"""
        try:
            from reports import ReportGenerator

            report_gen = ReportGenerator(self.db)

            # اختيار نوع التصدير
            format_choice, ok = QInputDialog.getItem(
                self, "نوع التقرير", "اختر نوع التصدير:",
                ["PDF", "Excel"], 0, False
            )

            if ok:
                filename = report_gen.generate_daily_report(export_format=format_choice.lower())
                create_arabic_info_box(self, "📊 تم إنشاء التقرير", f"تم حفظ التقرير في:\n{filename}")

        except ImportError:
            create_arabic_info_box(self, "📅 تقرير يومي", "سيتم إضافة هذه الميزة قريباً")

    def generate_monthly_report(self):
        """إنشاء تقرير شهري"""
        try:
            from reports import ReportGenerator

            report_gen = ReportGenerator(self.db)

            # اختيار نوع التصدير
            format_choice, ok = QInputDialog.getItem(
                self, "نوع التقرير", "اختر نوع التصدير:",
                ["PDF", "Excel"], 0, False
            )

            if ok:
                filename = report_gen.generate_monthly_report(export_format=format_choice.lower())
                create_arabic_info_box(self, "📊 تم إنشاء التقرير", f"تم حفظ التقرير في:\n{filename}")

        except ImportError:
            create_arabic_info_box(self, "📈 تقرير شهري", "سيتم إضافة هذه الميزة قريباً")

    def manage_exchange_rates(self):
        """إدارة أسعار الصرف"""
        try:
            from transaction_dialog import ExchangeRateDialog
            dialog = ExchangeRateDialog(self.db)
            dialog.exec_()
        except ImportError:
            create_arabic_info_box(self, "💱 أسعار الصرف", "سيتم إضافة هذه الميزة قريباً")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            from backup_manager import BackupManager, BackupDialog
            backup_manager = BackupManager()
            backup_dialog = BackupDialog(self, backup_manager)
            backup_dialog.show_backup_dialog()
        except ImportError:
            create_arabic_info_box(self, "💾 نسخة احتياطية", "سيتم إضافة هذه الميزة قريباً")

    def manage_users_and_permissions(self):
        """إدارة المستخدمين والصلاحيات"""
        # إنشاء نافذة إدارة المستخدمين
        self.show_users_management_dialog()

    def show_users_management_dialog(self):
        """عرض نافذة إدارة المستخدمين المؤقتة"""
        from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                                   QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                                   QComboBox, QGroupBox, QMessageBox, QHeaderView)

        dialog = QDialog(self)
        dialog.setWindowTitle("👥 إدارة المستخدمين والصلاحيات")
        dialog.setFixedSize(800, 600)
        dialog.setLayoutDirection(Qt.RightToLeft)

        # تطبيق الأنماط
        dialog.setStyleSheet("""
            QDialog {
                background: #f8f9fa;
                color: #2c3e50;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 10px 0;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background: #f8f9fa;
            }
            QLineEdit, QComboBox {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus, QComboBox:focus {
                border: 2px solid #3498db;
            }
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2980b9;
            }
            QPushButton:pressed {
                background: #21618c;
            }
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background: #3498db;
                color: white;
            }
            QHeaderView::section {
                background: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        layout = QVBoxLayout()

        # قسم إضافة مستخدم جديد
        add_user_group = QGroupBox("إضافة مستخدم جديد")
        add_user_layout = QVBoxLayout()

        # صف اسم المستخدم
        username_layout = QHBoxLayout()
        username_layout.addWidget(QLabel("اسم المستخدم:"))
        username_input = QLineEdit()
        username_input.setPlaceholderText("أدخل اسم المستخدم")
        username_layout.addWidget(username_input)
        add_user_layout.addLayout(username_layout)

        # صف كلمة المرور
        password_layout = QHBoxLayout()
        password_layout.addWidget(QLabel("كلمة المرور:"))
        password_input = QLineEdit()
        password_input.setEchoMode(QLineEdit.Password)
        password_input.setPlaceholderText("أدخل كلمة المرور")
        password_layout.addWidget(password_input)
        add_user_layout.addLayout(password_layout)

        # صف الصلاحية
        role_layout = QHBoxLayout()
        role_layout.addWidget(QLabel("الصلاحية:"))
        role_combo = QComboBox()
        role_combo.addItems(["مدير", "كاشير", "مدخل بيانات"])
        role_layout.addWidget(role_combo)
        add_user_layout.addLayout(role_layout)

        # زر الإضافة
        add_button = QPushButton("➕ إضافة مستخدم")
        add_user_layout.addWidget(add_button)

        add_user_group.setLayout(add_user_layout)
        layout.addWidget(add_user_group)

        # قسم قائمة المستخدمين
        users_group = QGroupBox("قائمة المستخدمين الحاليين")
        users_layout = QVBoxLayout()

        # جدول المستخدمين
        users_table = QTableWidget()
        users_table.setColumnCount(4)
        users_table.setHorizontalHeaderLabels(["اسم المستخدم", "الصلاحية", "تاريخ الإنشاء", "الإجراءات"])

        # تعبئة الجدول ببيانات وهمية
        sample_users = [
            ["admin", "مدير", "2024-01-01", ""],
            ["cashier1", "كاشير", "2024-01-15", ""],
            ["data_entry1", "مدخل بيانات", "2024-02-01", ""]
        ]

        users_table.setRowCount(len(sample_users))
        for row, user_data in enumerate(sample_users):
            for col, data in enumerate(user_data[:3]):
                users_table.setItem(row, col, QTableWidgetItem(data))

            # إضافة أزرار الإجراءات
            actions_layout = QHBoxLayout()
            edit_btn = QPushButton("تعديل")
            edit_btn.setStyleSheet("QPushButton { background: #f39c12; }")
            delete_btn = QPushButton("حذف")
            delete_btn.setStyleSheet("QPushButton { background: #e74c3c; }")

            actions_widget = QWidget()
            actions_widget.setLayout(actions_layout)
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)

            users_table.setCellWidget(row, 3, actions_widget)

        # تعديل عرض الأعمدة
        header = users_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)

        users_layout.addWidget(users_table)
        users_group.setLayout(users_layout)
        layout.addWidget(users_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.close)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_button)
        layout.addLayout(buttons_layout)

        # دالة إضافة مستخدم
        def add_new_user():
            username = username_input.text().strip()
            password = password_input.text().strip()
            role = role_combo.currentText()

            if not username or not password:
                create_arabic_info_box(dialog, "⚠️ خطأ", "يرجى ملء جميع الحقول المطلوبة", QMessageBox.Warning)
                return

            # إضافة المستخدم إلى الجدول
            row_count = users_table.rowCount()
            users_table.insertRow(row_count)
            users_table.setItem(row_count, 0, QTableWidgetItem(username))
            users_table.setItem(row_count, 1, QTableWidgetItem(role))
            users_table.setItem(row_count, 2, QTableWidgetItem("2024-06-27"))

            # إضافة أزرار الإجراءات للصف الجديد
            actions_layout = QHBoxLayout()
            edit_btn = QPushButton("تعديل")
            edit_btn.setStyleSheet("QPushButton { background: #f39c12; }")
            delete_btn = QPushButton("حذف")
            delete_btn.setStyleSheet("QPushButton { background: #e74c3c; }")

            actions_widget = QWidget()
            actions_widget.setLayout(actions_layout)
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)

            users_table.setCellWidget(row_count, 3, actions_widget)

            # مسح الحقول
            username_input.clear()
            password_input.clear()
            role_combo.setCurrentIndex(0)

            create_arabic_info_box(dialog, "✅ نجح", f"تم إضافة المستخدم '{username}' بنجاح")

        add_button.clicked.connect(add_new_user)

        dialog.setLayout(layout)
        dialog.exec_()

    def show_user_guide(self):
        """عرض دليل المستخدم"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout

        dialog = QDialog(self)
        dialog.setWindowTitle("📖 دليل المستخدم - نظام Flex USA")
        dialog.setFixedSize(800, 600)
        dialog.setLayoutDirection(Qt.RightToLeft)

        dialog.setStyleSheet("""
            QDialog {
                background: #f8f9fa;
                color: #2c3e50;
            }
            QTextEdit {
                background: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
                line-height: 1.6;
            }
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2980b9;
            }
        """)

        layout = QVBoxLayout()

        # محتوى دليل المستخدم
        guide_text = QTextEdit()
        guide_text.setReadOnly(True)
        guide_content = """
# دليل المستخدم - نظام Flex USA المحاسبي

## 🚀 البدء السريع

### تسجيل الدخول
- اسم المستخدم: admin
- كلمة المرور: admin123

### الواجهة الرئيسية
تحتوي الواجهة على:
- لوحة التحكم الرئيسية (عرض الأرصدة)
- جدول العمليات المالية
- شريط القوائم العلوي
- شريط الأدوات السريع

## 📊 إدارة العمليات المالية

### إضافة عملية جديدة
1. اضغط على "عملية جديدة" من شريط الأدوات
2. اختر نوع العملية (استلام/تسليم)
3. حدد العملة (دينار ليبي/دولار أمريكي)
4. أدخل المبلغ
5. أدخل بيانات الجهة والمرجع
6. اضغط "حفظ"

### عرض العمليات
- جميع العمليات تظهر في الجدول الرئيسي
- يمكن البحث والتصفية حسب التاريخ والنوع
- إمكانية تعديل أو حذف العمليات

## 📈 التقارير

### التقرير اليومي
- يعرض جميع عمليات اليوم الحالي
- يمكن تصديره بصيغة PDF أو Excel

### التقرير الشهري
- يعرض ملخص العمليات للشهر الحالي
- يتضمن إحصائيات مفصلة

## 🔧 الإعدادات

### أسعار الصرف
- تحديث أسعار صرف العملات
- إعداد الأسعار الافتراضية

### إدارة المستخدمين
- إضافة مستخدمين جدد
- تحديد الصلاحيات
- إدارة كلمات المرور

## 💾 النسخ الاحتياطية

### إنشاء نسخة احتياطية
- من قائمة "ملف" > "نسخة احتياطية"
- يتم حفظ النسخة في مجلد backups

### استرجاع النسخة
- اختيار النسخة المطلوبة
- تأكيد عملية الاسترجاع

## ⌨️ اختصارات لوحة المفاتيح

- Ctrl+N: عملية جديدة
- Ctrl+R: تحديث البيانات
- Ctrl+P: طباعة التقرير
- F5: تحديث الواجهة
- Ctrl+Q: خروج من البرنامج

## 🆘 الدعم الفني

في حالة مواجهة أي مشاكل:
1. تحقق من اتصال قاعدة البيانات
2. تأكد من صحة البيانات المدخلة
3. راجع ملف السجلات للأخطاء
4. تواصل مع فريق الدعم الفني

---
© 2024 Flex USA - جميع الحقوق محفوظة
        """
        guide_text.setPlainText(guide_content)
        layout.addWidget(guide_text)

        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.close)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)

        dialog.setLayout(layout)
        dialog.exec_()

    def show_keyboard_shortcuts(self):
        """عرض اختصارات لوحة المفاتيح"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout, QHeaderView

        dialog = QDialog(self)
        dialog.setWindowTitle("⌨️ اختصارات لوحة المفاتيح")
        dialog.setFixedSize(600, 400)
        dialog.setLayoutDirection(Qt.RightToLeft)

        dialog.setStyleSheet("""
            QDialog {
                background: #f8f9fa;
                color: #2c3e50;
            }
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background: #34495e;
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
            }
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2980b9;
            }
        """)

        layout = QVBoxLayout()

        # جدول الاختصارات
        shortcuts_table = QTableWidget()
        shortcuts_table.setColumnCount(2)
        shortcuts_table.setHorizontalHeaderLabels(["الاختصار", "الوظيفة"])

        shortcuts_data = [
            ["Ctrl+N", "عملية جديدة"],
            ["Ctrl+R", "تحديث البيانات"],
            ["Ctrl+P", "طباعة التقرير"],
            ["Ctrl+S", "حفظ"],
            ["Ctrl+F", "بحث"],
            ["F5", "تحديث الواجهة"],
            ["Ctrl+B", "نسخة احتياطية"],
            ["Ctrl+U", "إدارة المستخدمين"],
            ["Ctrl+E", "أسعار الصرف"],
            ["F1", "المساعدة"],
            ["Alt+F4", "إغلاق البرنامج"],
            ["Ctrl+Q", "خروج سريع"]
        ]

        shortcuts_table.setRowCount(len(shortcuts_data))
        for row, (shortcut, function) in enumerate(shortcuts_data):
            shortcuts_table.setItem(row, 0, QTableWidgetItem(shortcut))
            shortcuts_table.setItem(row, 1, QTableWidgetItem(function))

        # تعديل عرض الأعمدة
        header = shortcuts_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        layout.addWidget(shortcuts_table)

        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.close)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)

        dialog.setLayout(layout)
        dialog.exec_()

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout

        dialog = QDialog(self)
        dialog.setWindowTitle("ℹ️ حول البرنامج")
        dialog.setFixedSize(500, 400)
        dialog.setLayoutDirection(Qt.RightToLeft)

        dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
            }
            QLabel {
                background: transparent;
                color: white;
                text-align: center;
            }
            QPushButton {
                background: rgba(255,255,255,0.2);
                color: white;
                border: 2px solid white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(255,255,255,0.3);
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(20)

        # شعار البرنامج
        logo_label = QLabel("💰")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("font-size: 64px; margin: 20px;")
        layout.addWidget(logo_label)

        # اسم البرنامج
        title_label = QLabel("نظام Flex USA المحاسبي")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)

        # الإصدار
        version_label = QLabel("الإصدار 1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("font-size: 16px; margin: 5px;")
        layout.addWidget(version_label)

        # الوصف
        description_label = QLabel("نظام شامل لإدارة العمليات المالية\nيدعم العملات المتعددة والتقارير المفصلة")
        description_label.setAlignment(Qt.AlignCenter)
        description_label.setStyleSheet("font-size: 14px; margin: 15px; line-height: 1.5;")
        layout.addWidget(description_label)

        # معلومات المطور
        developer_label = QLabel("تطوير: فريق Flex USA\nتاريخ الإصدار: 2024-06-27")
        developer_label.setAlignment(Qt.AlignCenter)
        developer_label.setStyleSheet("font-size: 12px; margin: 10px;")
        layout.addWidget(developer_label)

        # حقوق النشر
        copyright_label = QLabel("© 2024 Flex USA - جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("font-size: 10px; margin: 10px; opacity: 0.8;")
        layout.addWidget(copyright_label)

        layout.addStretch()

        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.close)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        dialog.setLayout(layout)
        dialog.exec_()

    def logout(self):
        """تسجيل خروج المستخدم"""

        if create_arabic_message_box(
            self,
            "🔓 تسجيل الخروج",
            "هل أنت متأكد من تسجيل الخروج؟\nسيتم إغلاق البرنامج وإعادة فتح شاشة تسجيل الدخول."
        ):
            self.current_user = None
            self.close()

            # إعادة تشغيل شاشة تسجيل الدخول
            if self.login():
                self.show()
            else:
                import sys
                sys.exit(0)

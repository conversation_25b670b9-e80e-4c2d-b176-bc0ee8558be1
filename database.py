import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path="flex_usa.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول الأساسية"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'cashier',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العمليات المالية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_type TEXT NOT NULL,
                currency TEXT NOT NULL,
                amount REAL NOT NULL,
                exchange_rate REAL DEFAULT 1.0,
                date DATE NOT NULL,
                party_name TEXT NOT NULL,
                reference_number TEXT,
                description TEXT,
                user_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول أسعار الصرف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS exchange_rates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                from_currency TEXT NOT NULL,
                to_currency TEXT NOT NULL,
                rate REAL NOT NULL,
                date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إضافة مستخدم افتراضي
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password, role) 
            VALUES ('admin', 'admin123', 'admin')
        ''')
        
        conn.commit()
        conn.close()
    
    def add_transaction(self, transaction_type, currency, amount, exchange_rate, date, 
                       party_name, reference_number, description, user_id):
        """إضافة عملية مالية جديدة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO transactions 
            (transaction_type, currency, amount, exchange_rate, date, party_name, 
             reference_number, description, user_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (transaction_type, currency, amount, exchange_rate, date, 
              party_name, reference_number, description, user_id))
        
        conn.commit()
        conn.close()
    
    def get_transactions(self, start_date=None, end_date=None, currency=None):
        """استرجاع العمليات المالية مع إمكانية التصفية"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = '''
            SELECT t.*, u.username 
            FROM transactions t 
            LEFT JOIN users u ON t.user_id = u.id
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            query += " AND t.date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND t.date <= ?"
            params.append(end_date)
            
        if currency:
            query += " AND t.currency = ?"
            params.append(currency)
        
        query += " ORDER BY t.date DESC, t.created_at DESC"
        
        cursor.execute(query, params)
        transactions = cursor.fetchall()
        conn.close()
        
        return transactions
    
    def get_balance_summary(self):
        """حساب الرصيد الإجمالي بالعملتين"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # رصيد الدينار الليبي
        cursor.execute('''
            SELECT 
                SUM(CASE WHEN transaction_type = 'استلام' THEN amount ELSE 0 END) as received_lyd,
                SUM(CASE WHEN transaction_type = 'تسليم' THEN amount ELSE 0 END) as paid_lyd
            FROM transactions WHERE currency = 'دينار ليبي'
        ''')
        lyd_result = cursor.fetchone()
        
        # رصيد الدولار الأمريكي
        cursor.execute('''
            SELECT 
                SUM(CASE WHEN transaction_type = 'استلام' THEN amount ELSE 0 END) as received_usd,
                SUM(CASE WHEN transaction_type = 'تسليم' THEN amount ELSE 0 END) as paid_usd
            FROM transactions WHERE currency = 'دولار أمريكي'
        ''')
        usd_result = cursor.fetchone()
        
        conn.close()
        
        return {
            'lyd': {
                'received': lyd_result[0] or 0,
                'paid': lyd_result[1] or 0,
                'balance': (lyd_result[0] or 0) - (lyd_result[1] or 0)
            },
            'usd': {
                'received': usd_result[0] or 0,
                'paid': usd_result[1] or 0,
                'balance': (usd_result[0] or 0) - (usd_result[1] or 0)
            }
        }
    
    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, role FROM users 
            WHERE username = ? AND password = ?
        ''', (username, password))
        
        user = cursor.fetchone()
        conn.close()
        
        return user
    
    def add_exchange_rate(self, from_currency, to_currency, rate, date):
        """إضافة سعر صرف جديد"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO exchange_rates (from_currency, to_currency, rate, date)
            VALUES (?, ?, ?, ?)
        ''', (from_currency, to_currency, rate, date))
        
        conn.commit()
        conn.close()
    
    def get_latest_exchange_rate(self, from_currency, to_currency):
        """الحصول على آخر سعر صرف"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT rate FROM exchange_rates 
            WHERE from_currency = ? AND to_currency = ?
            ORDER BY date DESC, created_at DESC
            LIMIT 1
        ''', (from_currency, to_currency))
        
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result else 1.0

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    from main_window import MainWindow
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط
    font = QFont("Segoe UI", 12)
    app.setFont(font)
    
    # إنشاء المجلدات
    for directory in ['assets', 'backups', 'reports', 'invoices', 'temp']:
        if not os.path.exists(directory):
            os.makedirs(directory)
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    
    # تسجيل الدخول
    if main_window.login():
        main_window.show()
        sys.exit(app.exec_())
    else:
        sys.exit(0)
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

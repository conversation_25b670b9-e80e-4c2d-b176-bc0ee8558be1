#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTranslator, QLocale
from main_window import MainWindow

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    
    # إنشاء تطبيق PyQt5
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية (إذا كان متاحاً)
    app.setLayoutDirection(2)  # RTL للعربية
    
    # إعداد أيقونة التطبيق
    app.setApplicationName("Flex USA - نظام المحاسبة المالي")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Flex USA")
    
    try:
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        # محاولة تسجيل الدخول
        if main_window.login():
            # عرض النافذة الرئيسية
            main_window.show()
            
            # تشغيل حلقة الأحداث
            sys.exit(app.exec_())
        else:
            # إغلاق التطبيق إذا فشل تسجيل الدخول
            sys.exit(0)
            
    except Exception as e:
        # عرض رسالة خطأ في حالة حدوث مشكلة
        QMessageBox.critical(None, "خطأ في التطبيق", 
                           f"حدث خطأ أثناء تشغيل التطبيق:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()

# نظام Flex USA المحاسبي

## نظرة عامة
نظام محاسبة مالي شامل مصمم خصيصاً للمحلات التي تشتري البضائع من الإنترنت وتتعامل بعملتي الدينار الليبي والدولار الأمريكي.

## المميزات الرئيسية

### 🔐 إدارة المستخدمين
- تسجيل دخول آمن
- صلاحيات متعددة (مدير، كاشير، مدخل بيانات)
- إدارة حسابات المستخدمين

### 💰 إدارة العمليات المالية
- تسجيل عمليات الاستلام والتسليم
- دعم العملتين: الدينار الليبي والدولار الأمريكي
- تتبع أسعار الصرف
- ربط العمليات بالجهات والمراجع

### 📊 لوحة التحكم
- عرض الأرصدة الحالية بالعملتين
- إحصائيات سريعة للعمليات
- متابعة المبالغ المستلمة والمسلمة

### 📈 التقارير
- تقارير يومية وشهرية
- تقارير حسب العملة
- تصدير بصيغ PDF و Excel
- إحصائيات الربح والخسارة

### 🔄 النسخ الاحتياطية
- إنشاء نسخ احتياطية تلقائية
- استرجاع البيانات
- إدارة النسخ الاحتياطية

## متطلبات التشغيل

### البرامج المطلوبة
- Python 3.7 أو أحدث
- PyQt5
- SQLite3

### المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd flex-usa-accounting
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python main.py
```

## بيانات الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## هيكل المشروع

```
flex-usa-accounting/
├── main.py                 # الملف الرئيسي لتشغيل التطبيق
├── main_window.py          # النافذة الرئيسية
├── database.py             # إدارة قاعدة البيانات
├── transaction_dialog.py   # نوافذ العمليات المالية
├── reports.py              # مولد التقارير
├── backup_manager.py       # إدارة النسخ الاحتياطية
├── requirements.txt        # المكتبات المطلوبة
├── README.md              # دليل الاستخدام
├── backups/               # مجلد النسخ الاحتياطية
└── reports/               # مجلد التقارير المُصدرة
```

## الاستخدام

### إضافة عملية مالية جديدة
1. اضغط على "عملية جديدة" من شريط الأدوات
2. اختر نوع العملية (استلام/تسليم)
3. حدد العملة والمبلغ
4. أدخل بيانات الجهة والمرجع
5. اضغط "حفظ"

### إنشاء التقارير
1. من قائمة "التقارير" اختر نوع التقرير المطلوب
2. حدد نوع التصدير (PDF أو Excel)
3. سيتم حفظ التقرير في مجلد "reports"

### إدارة النسخ الاحتياطية
1. من قائمة "ملف" اختر "نسخة احتياطية"
2. يمكنك إنشاء، استرجاع، أو حذف النسخ الاحتياطية

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل تقديم أي تعديلات.

---

**Flex USA - نظام المحاسبة المالي**  
الإصدار 1.0.0

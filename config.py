#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
from datetime import datetime

class Config:
    """إعدادات التطبيق"""
    
    # إعدادات قاعدة البيانات
    DATABASE_PATH = "flex_usa.db"
    
    # إعدادات المجلدات
    BACKUP_DIR = "backups"
    REPORTS_DIR = "reports"
    TEMP_DIR = "temp"
    
    # إعدادات التطبيق
    APP_NAME = "Flex USA - نظام المحاسبة المالي"
    APP_VERSION = "1.0.0"
    APP_AUTHOR = "Flex USA Team"
    
    # إعدادات العملات
    CURRENCIES = {
        "LYD": {
            "name": "دينار ليبي",
            "symbol": "د.ل",
            "code": "LYD"
        },
        "USD": {
            "name": "دولار أمريكي", 
            "symbol": "$",
            "code": "USD"
        }
    }
    
    # أنواع العمليات
    TRANSACTION_TYPES = [
        "استلام",
        "تسليم"
    ]
    
    # أدوار المستخدمين
    USER_ROLES = {
        "admin": "مدير",
        "cashier": "كاشير",
        "data_entry": "مدخل بيانات"
    }
    
    # إعدادات التقارير
    REPORT_FORMATS = ["PDF", "Excel"]
    
    # إعدادات النسخ الاحتياطية
    AUTO_BACKUP_ENABLED = True
    BACKUP_RETENTION_DAYS = 30
    
    # إعدادات أسعار الصرف الافتراضية
    DEFAULT_EXCHANGE_RATES = {
        "USD_TO_LYD": 4.8000,
        "LYD_TO_USD": 0.2083
    }
    
    @classmethod
    def create_directories(cls):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            cls.BACKUP_DIR,
            cls.REPORTS_DIR,
            cls.TEMP_DIR
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    @classmethod
    def get_config_file_path(cls):
        """مسار ملف الإعدادات"""
        return "config.json"
    
    @classmethod
    def load_user_config(cls):
        """تحميل إعدادات المستخدم"""
        config_file = cls.get_config_file_path()
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"خطأ في تحميل الإعدادات: {e}")
                return {}
        
        return {}
    
    @classmethod
    def save_user_config(cls, config_data):
        """حفظ إعدادات المستخدم"""
        config_file = cls.get_config_file_path()
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    @classmethod
    def get_default_config(cls):
        """الإعدادات الافتراضية"""
        return {
            "app": {
                "name": cls.APP_NAME,
                "version": cls.APP_VERSION,
                "author": cls.APP_AUTHOR
            },
            "database": {
                "path": cls.DATABASE_PATH,
                "auto_backup": cls.AUTO_BACKUP_ENABLED
            },
            "directories": {
                "backup": cls.BACKUP_DIR,
                "reports": cls.REPORTS_DIR,
                "temp": cls.TEMP_DIR
            },
            "currencies": cls.CURRENCIES,
            "exchange_rates": cls.DEFAULT_EXCHANGE_RATES,
            "backup": {
                "retention_days": cls.BACKUP_RETENTION_DAYS,
                "auto_backup": cls.AUTO_BACKUP_ENABLED
            },
            "ui": {
                "language": "ar",
                "theme": "default",
                "window_size": {
                    "width": 1200,
                    "height": 800
                }
            },
            "reports": {
                "default_format": "PDF",
                "auto_open": True
            }
        }

class AppSettings:
    """إدارة إعدادات التطبيق"""
    
    def __init__(self):
        self.config = Config.load_user_config()
        if not self.config:
            self.config = Config.get_default_config()
            self.save()
    
    def get(self, key, default=None):
        """الحصول على قيمة إعداد"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key, value):
        """تعيين قيمة إعداد"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self):
        """حفظ الإعدادات"""
        return Config.save_user_config(self.config)
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        self.config = Config.get_default_config()
        return self.save()
    
    def get_database_path(self):
        """مسار قاعدة البيانات"""
        return self.get('database.path', Config.DATABASE_PATH)
    
    def get_backup_dir(self):
        """مجلد النسخ الاحتياطية"""
        return self.get('directories.backup', Config.BACKUP_DIR)
    
    def get_reports_dir(self):
        """مجلد التقارير"""
        return self.get('directories.reports', Config.REPORTS_DIR)
    
    def is_auto_backup_enabled(self):
        """هل النسخ الاحتياطي التلقائي مفعل"""
        return self.get('backup.auto_backup', Config.AUTO_BACKUP_ENABLED)
    
    def get_backup_retention_days(self):
        """عدد أيام الاحتفاظ بالنسخ الاحتياطية"""
        return self.get('backup.retention_days', Config.BACKUP_RETENTION_DAYS)
    
    def get_default_exchange_rate(self, from_currency, to_currency):
        """سعر الصرف الافتراضي"""
        key = f"{from_currency}_TO_{to_currency}"
        return self.get(f'exchange_rates.{key}', 1.0)

# إنشاء مثيل إعدادات التطبيق
app_settings = AppSettings()

# إنشاء المجلدات المطلوبة عند تحميل الوحدة
Config.create_directories()

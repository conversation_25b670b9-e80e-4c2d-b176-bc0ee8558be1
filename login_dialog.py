#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة تسجيل الدخول الاحترافية لنظام Flex USA المحاسبي
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from styles import LoginStyles

class ProfessionalLoginDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("تسجيل الدخول - Flex USA")
        self.setFixedSize(450, 600)
        self.setWindowFlags(Qt.Dialog | Qt.MSWindowsFixedSizeDialogHint)

        # إعداد الاتجاه من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

        # إعداد الخلفية والستايل العام مع دعم RTL
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                qproperty-layoutDirection: RightToLeft;
            }
            * {
                font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
                direction: rtl;
            }
            QLabel {
                text-align: center;
                qproperty-alignment: AlignCenter;
            }
            QLineEdit {
                text-align: right;
                qproperty-alignment: AlignRight;
            }
            QCheckBox {
                text-align: right;
            }
        """)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء الأقسام
        self.create_header_section(main_layout)
        self.create_form_section(main_layout)
        self.create_footer_section(main_layout)
        
        self.setLayout(main_layout)
        
        # ربط الأحداث
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.password_input.returnPressed.connect(self.handle_login)
        self.username_input.textChanged.connect(self.validate_inputs)
        self.password_input.textChanged.connect(self.validate_inputs)
        
        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()
        
        # تأثيرات بصرية
        self.add_visual_effects()
        
        # حالة الزر الأولية
        self.login_button.setEnabled(False)
        
        # إضافة تأثير الحركة
        self.setup_animations()
    
    def create_header_section(self, main_layout):
        """إنشاء قسم الرأس مع الشعار والعنوان"""
        header_widget = QWidget()
        header_widget.setFixedHeight(200)
        header_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.1), stop:1 rgba(255,255,255,0.05));
                border-top-left-radius: 15px;
                border-top-right-radius: 15px;
            }
        """)
        
        header_layout = QVBoxLayout()
        header_layout.setContentsMargins(20, 30, 20, 20)
        
        # شعار النظام مع تأثير متحرك
        logo_label = QLabel("💰")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 64px;
                color: white;
                background: transparent;
                margin: 15px;
            }
        """)
        header_layout.addWidget(logo_label)
        
        # عنوان النظام
        title_label = QLabel("Flex USA")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: white;
                background: transparent;
                margin: 5px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
        """)
        header_layout.addWidget(title_label)
        
        # نص فرعي
        subtitle_label = QLabel("نظام إدارة العمليات المالية")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: rgba(255,255,255,0.9);
                background: transparent;
                margin: 5px;
            }
        """)
        header_layout.addWidget(subtitle_label)
        
        header_widget.setLayout(header_layout)
        main_layout.addWidget(header_widget)
    
    def create_form_section(self, main_layout):
        """إنشاء قسم النموذج"""
        form_widget = QWidget()
        form_widget.setStyleSheet("""
            QWidget {
                background: rgba(255,255,255,0.95);
                border: none;
            }
        """)
        
        form_layout = QVBoxLayout()
        form_layout.setContentsMargins(40, 40, 40, 30)
        form_layout.setSpacing(25)
        
        # عنوان تسجيل الدخول
        login_title = QLabel("تسجيل الدخول")
        login_title.setAlignment(Qt.AlignCenter)
        login_title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        form_layout.addWidget(login_title)
        
        # حقل اسم المستخدم
        username_container = self.create_modern_input_field("👤", "اسم المستخدم")
        self.username_input = username_container.findChild(QLineEdit)
        form_layout.addWidget(username_container)
        
        # حقل كلمة المرور
        password_container = self.create_modern_input_field("🔒", "كلمة المرور", is_password=True)
        self.password_input = password_container.findChild(QLineEdit)
        form_layout.addWidget(password_container)
        
        # خيارات إضافية
        options_layout = QHBoxLayout()
        
        self.remember_checkbox = QCheckBox("تذكر كلمة المرور")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: #7f8c8d;
                font-size: 13px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                background: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #667eea;
                border-radius: 4px;
                background: #667eea;
            }
        """)
        
        forgot_password_label = QLabel('<a href="#" style="color: #667eea; text-decoration: none;">نسيت كلمة المرور؟</a>')
        forgot_password_label.setAlignment(Qt.AlignRight)
        forgot_password_label.setStyleSheet("font-size: 12px;")
        
        options_layout.addWidget(self.remember_checkbox)
        options_layout.addStretch()
        options_layout.addWidget(forgot_password_label)
        form_layout.addLayout(options_layout)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        self.cancel_button = self.create_modern_button("إلغاء", "#95a5a6", "#7f8c8d")
        self.login_button = self.create_modern_button("دخول", "#667eea", "#764ba2", is_primary=True)
        
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        form_layout.addLayout(buttons_layout)
        
        form_widget.setLayout(form_layout)
        main_layout.addWidget(form_widget)
    
    def create_modern_input_field(self, icon, placeholder, is_password=False):
        """إنشاء حقل إدخال عصري مع أيقونة"""
        container = QWidget()
        container.setFixedHeight(55)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # أيقونة
        icon_label = QLabel(icon)
        icon_label.setFixedSize(55, 55)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-top-left-radius: 27px;
                border-bottom-left-radius: 27px;
                font-size: 18px;
                color: white;
            }
        """)
        
        # حقل الإدخال
        input_field = QLineEdit()
        input_field.setPlaceholderText(placeholder)
        input_field.setFixedHeight(55)
        
        if is_password:
            input_field.setEchoMode(QLineEdit.Password)
        
        input_field.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-left: none;
                border-top-right-radius: 27px;
                border-bottom-right-radius: 27px;
                padding: 0 20px;
                font-size: 14px;
                background: white;
                color: #2c3e50;
                text-align: right;
                qproperty-alignment: AlignRight;
            }
            QLineEdit:focus {
                border-color: #667eea;
                outline: none;
            }
        """)
        
        layout.addWidget(icon_label)
        layout.addWidget(input_field)
        
        container.setLayout(layout)
        return container
    
    def create_modern_button(self, text, color1, color2, is_primary=False):
        """إنشاء زر عصري مع تدرج لوني"""
        button = QPushButton(text)
        button.setFixedHeight(50)
        button.setMinimumWidth(120)
        
        if is_primary:
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {color1}, stop:1 {color2});
                    color: white;
                    border: none;
                    border-radius: 25px;
                    font-size: 15px;
                    font-weight: bold;
                    padding: 10px 25px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #7c8df0, stop:1 #8a5fb8);
                    transform: translateY(-2px);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #5a6fd8, stop:1 #6b4190);
                }}
                QPushButton:disabled {{
                    background: #bdc3c7;
                    color: #7f8c8d;
                }}
            """)
        else:
            button.setStyleSheet(f"""
                QPushButton {{
                    background: {color1};
                    color: white;
                    border: none;
                    border-radius: 25px;
                    font-size: 15px;
                    font-weight: bold;
                    padding: 10px 25px;
                }}
                QPushButton:hover {{
                    background: {color2};
                }}
                QPushButton:pressed {{
                    background: #6c7b7d;
                }}
            """)
        
        return button
    
    def create_footer_section(self, main_layout):
        """إنشاء قسم التذييل"""
        footer_widget = QWidget()
        footer_widget.setFixedHeight(90)
        footer_widget.setStyleSheet("""
            QWidget {
                background: rgba(255,255,255,0.1);
                border-bottom-left-radius: 15px;
                border-bottom-right-radius: 15px;
            }
        """)
        
        footer_layout = QVBoxLayout()
        footer_layout.setContentsMargins(20, 20, 20, 20)
        
        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                color: rgba(255,255,255,0.8);
                font-size: 13px;
                background: transparent;
            }
        """)
        footer_layout.addWidget(version_label)
        
        # حقوق الطبع
        copyright_label = QLabel("© 2024 Flex USA - جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: rgba(255,255,255,0.6);
                font-size: 11px;
                background: transparent;
            }
        """)
        footer_layout.addWidget(copyright_label)
        
        footer_widget.setLayout(footer_layout)
        main_layout.addWidget(footer_widget)
    
    def setup_animations(self):
        """إعداد الحركات والتأثيرات المتحركة"""
        # تأثير الظهور التدريجي
        self.fade_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.fade_effect)
        
        self.fade_animation = QPropertyAnimation(self.fade_effect, b"opacity")
        self.fade_animation.setDuration(500)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()
    
    def validate_inputs(self):
        """التحقق من صحة المدخلات وتفعيل/إلغاء تفعيل زر الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        self.login_button.setEnabled(bool(username and password))
    
    def handle_login(self):
        """معالجة عملية تسجيل الدخول مع تأثيرات بصرية"""
        if not self.username_input.text().strip() or not self.password_input.text().strip():
            self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # تأثير التحميل
        original_text = self.login_button.text()
        self.login_button.setText("جاري التحقق...")
        self.login_button.setEnabled(False)

        # تأخير قصير لإظهار تأثير التحميل ثم إغلاق النافذة
        QTimer.singleShot(500, lambda: self.complete_login(original_text))

    def complete_login(self, original_text):
        """إكمال عملية تسجيل الدخول"""
        self.login_button.setText(original_text)
        self.login_button.setEnabled(True)
        self.accept()
    
    def show_error_message(self, message):
        """عرض رسالة خطأ مع تأثيرات بصرية"""
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setWindowTitle("تنبيه")
        msg_box.setText(message)
        msg_box.setStyleSheet("""
            QMessageBox {
                background: white;
                color: #2c3e50;
            }
            QMessageBox QPushButton {
                background: #667eea;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QMessageBox QPushButton:hover {
                background: #764ba2;
            }
        """)
        msg_box.exec_()
    
    def add_visual_effects(self):
        """إضافة تأثيرات بصرية"""
        # تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setXOffset(0)
        shadow.setYOffset(15)
        shadow.setColor(QColor(0, 0, 0, 100))
        self.setGraphicsEffect(shadow)

import os
import shutil
import sqlite3
from datetime import datetime
import zipfile
from PyQt5.QtWidgets import QMessageBox, QFileDialog

class BackupManager:
    def __init__(self, db_path="flex_usa.db"):
        self.db_path = db_path
        self.backup_dir = "backups"
        
        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def create_backup(self, backup_name=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_{timestamp}"
            
            # مسار النسخة الاحتياطية
            backup_file = os.path.join(self.backup_dir, f"{backup_name}.db")
            
            # نسخ قاعدة البيانات
            if os.path.exists(self.db_path):
                shutil.copy2(self.db_path, backup_file)
                
                # إنشاء ملف مضغوط يحتوي على النسخة الاحتياطية
                zip_file = os.path.join(self.backup_dir, f"{backup_name}.zip")
                with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    zipf.write(backup_file, f"{backup_name}.db")
                    
                    # إضافة معلومات النسخة الاحتياطية
                    backup_info = self._get_backup_info()
                    zipf.writestr("backup_info.txt", backup_info)
                
                # حذف الملف غير المضغوط
                os.remove(backup_file)
                
                return zip_file
            else:
                raise FileNotFoundError("قاعدة البيانات غير موجودة")
                
        except Exception as e:
            raise Exception(f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
    
    def restore_backup(self, backup_file):
        """استرجاع نسخة احتياطية"""
        try:
            if not os.path.exists(backup_file):
                raise FileNotFoundError("ملف النسخة الاحتياطية غير موجود")
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستبدال
            current_backup = self.create_backup("before_restore")
            
            # استخراج النسخة الاحتياطية
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                # البحث عن ملف قاعدة البيانات في الأرشيف
                db_files = [f for f in zipf.namelist() if f.endswith('.db')]
                if not db_files:
                    raise Exception("لا يحتوي الأرشيف على ملف قاعدة بيانات صالح")
                
                # استخراج قاعدة البيانات
                zipf.extract(db_files[0], "temp_restore")
                
                # نسخ قاعدة البيانات المستخرجة
                extracted_db = os.path.join("temp_restore", db_files[0])
                shutil.copy2(extracted_db, self.db_path)
                
                # تنظيف الملفات المؤقتة
                shutil.rmtree("temp_restore")
            
            return True
            
        except Exception as e:
            raise Exception(f"فشل في استرجاع النسخة الاحتياطية: {str(e)}")
    
    def list_backups(self):
        """قائمة بجميع النسخ الاحتياطية المتاحة"""
        backups = []
        
        if os.path.exists(self.backup_dir):
            for file in os.listdir(self.backup_dir):
                if file.endswith('.zip'):
                    file_path = os.path.join(self.backup_dir, file)
                    file_stat = os.stat(file_path)
                    
                    backups.append({
                        'name': file,
                        'path': file_path,
                        'size': file_stat.st_size,
                        'created': datetime.fromtimestamp(file_stat.st_ctime),
                        'modified': datetime.fromtimestamp(file_stat.st_mtime)
                    })
        
        # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        backups.sort(key=lambda x: x['created'], reverse=True)
        return backups
    
    def delete_backup(self, backup_path):
        """حذف نسخة احتياطية"""
        try:
            if os.path.exists(backup_path):
                os.remove(backup_path)
                return True
            else:
                raise FileNotFoundError("ملف النسخة الاحتياطية غير موجود")
        except Exception as e:
            raise Exception(f"فشل في حذف النسخة الاحتياطية: {str(e)}")
    
    def _get_backup_info(self):
        """الحصول على معلومات النسخة الاحتياطية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # عدد المستخدمين
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            
            # عدد العمليات
            cursor.execute("SELECT COUNT(*) FROM transactions")
            transactions_count = cursor.fetchone()[0]
            
            # عدد أسعار الصرف
            cursor.execute("SELECT COUNT(*) FROM exchange_rates")
            rates_count = cursor.fetchone()[0]
            
            conn.close()
            
            info = f"""معلومات النسخة الاحتياطية
تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
عدد المستخدمين: {users_count}
عدد العمليات: {transactions_count}
عدد أسعار الصرف: {rates_count}
نظام: Flex USA المحاسبي
الإصدار: 1.0.0
"""
            return info
            
        except Exception as e:
            return f"خطأ في الحصول على معلومات النسخة الاحتياطية: {str(e)}"
    
    def auto_backup(self):
        """إنشاء نسخة احتياطية تلقائية"""
        try:
            # التحقق من آخر نسخة احتياطية
            backups = self.list_backups()
            
            # إنشاء نسخة احتياطية إذا لم تكن هناك نسخة اليوم
            today = datetime.now().date()
            need_backup = True
            
            if backups:
                latest_backup = backups[0]
                if latest_backup['created'].date() == today:
                    need_backup = False
            
            if need_backup:
                backup_file = self.create_backup(f"auto_backup_{today.strftime('%Y%m%d')}")
                return backup_file
            
            return None
            
        except Exception as e:
            print(f"خطأ في النسخة الاحتياطية التلقائية: {str(e)}")
            return None

class BackupDialog:
    def __init__(self, parent, backup_manager):
        self.parent = parent
        self.backup_manager = backup_manager
    
    def show_backup_dialog(self):
        """عرض نافذة إدارة النسخ الاحتياطية"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QListWidget, QLabel
        from PyQt5.QtCore import Qt

        dialog = QDialog(self.parent)
        dialog.setWindowTitle("💾 إدارة النسخ الاحتياطية")
        dialog.setFixedSize(600, 400)

        # إعداد الاتجاه من اليمين إلى اليسار
        dialog.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("إدارة النسخ الاحتياطية")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # قائمة النسخ الاحتياطية
        self.backup_list = QListWidget()
        self.load_backup_list()
        layout.addWidget(self.backup_list)
        
        # أزرار التحكم
        button_layout = QHBoxLayout()
        
        create_btn = QPushButton("إنشاء نسخة احتياطية")
        create_btn.clicked.connect(self.create_backup)
        button_layout.addWidget(create_btn)
        
        restore_btn = QPushButton("استرجاع")
        restore_btn.clicked.connect(self.restore_backup)
        button_layout.addWidget(restore_btn)
        
        delete_btn = QPushButton("حذف")
        delete_btn.clicked.connect(self.delete_backup)
        button_layout.addWidget(delete_btn)
        
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(dialog.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        dialog.setLayout(layout)
        
        dialog.exec_()
    
    def load_backup_list(self):
        """تحميل قائمة النسخ الاحتياطية"""
        self.backup_list.clear()
        backups = self.backup_manager.list_backups()
        
        for backup in backups:
            item_text = f"{backup['name']} - {backup['created'].strftime('%Y-%m-%d %H:%M')} - {backup['size']/1024:.1f} KB"
            self.backup_list.addItem(item_text)
            self.backup_list.item(self.backup_list.count()-1).setData(32, backup['path'])
    
    def create_backup(self):
        """إنشاء نسخة احتياطية جديدة"""
        try:
            backup_file = self.backup_manager.create_backup()
            QMessageBox.information(self.parent, "نجح الحفظ", f"تم إنشاء النسخة الاحتياطية:\n{backup_file}")
            self.load_backup_list()
        except Exception as e:
            QMessageBox.critical(self.parent, "خطأ", str(e))
    
    def restore_backup(self):
        """استرجاع نسخة احتياطية"""
        current_item = self.backup_list.currentItem()
        if not current_item:
            QMessageBox.warning(self.parent, "تحذير", "يرجى اختيار نسخة احتياطية للاسترجاع")
            return
        
        backup_path = current_item.data(32)
        
        reply = QMessageBox.question(
            self.parent, "تأكيد الاسترجاع",
            "هل أنت متأكد من استرجاع هذه النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية.",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.backup_manager.restore_backup(backup_path)
                QMessageBox.information(self.parent, "نجح الاسترجاع", "تم استرجاع النسخة الاحتياطية بنجاح")
            except Exception as e:
                QMessageBox.critical(self.parent, "خطأ", str(e))
    
    def delete_backup(self):
        """حذف نسخة احتياطية"""
        current_item = self.backup_list.currentItem()
        if not current_item:
            QMessageBox.warning(self.parent, "تحذير", "يرجى اختيار نسخة احتياطية للحذف")
            return
        
        backup_path = current_item.data(32)
        
        reply = QMessageBox.question(
            self.parent, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذه النسخة الاحتياطية؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.backup_manager.delete_backup(backup_path)
                QMessageBox.information(self.parent, "نجح الحذف", "تم حذف النسخة الاحتياطية")
                self.load_backup_list()
            except Exception as e:
                QMessageBox.critical(self.parent, "خطأ", str(e))

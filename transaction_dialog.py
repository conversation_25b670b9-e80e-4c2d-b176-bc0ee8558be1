from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

def create_arabic_info_box(parent, title, text, icon=QMessageBox.Information):
    """إنشاء نافذة رسالة معلومات عربية"""
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(text)
    msg_box.setIcon(icon)
    msg_box.setLayoutDirection(Qt.RightToLeft)

    # إضافة زر موافق عربي
    ok_button = msg_box.addButton("موافق", QMessageBox.AcceptRole)
    msg_box.setDefaultButton(ok_button)

    # تطبيق الأنماط العربية
    if icon == QMessageBox.Information:
        button_color = "#27ae60"
        button_hover = "#2ecc71"
    elif icon == QMessageBox.Warning:
        button_color = "#f39c12"
        button_hover = "#e67e22"
    elif icon == QMessageBox.Critical:
        button_color = "#e74c3c"
        button_hover = "#c0392b"
    else:
        button_color = "#3498db"
        button_hover = "#2980b9"

    msg_box.setStyleSheet(f"""
        QMessageBox {{
            background: white;
            color: #2c3e50;
            font-family: "Arial";
            qproperty-layoutDirection: RightToLeft;
        }}
        QMessageBox QPushButton {{
            background: {button_color};
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-weight: bold;
            min-width: 80px;
            font-size: 12px;
        }}
        QMessageBox QPushButton:hover {{
            background: {button_hover};
        }}
        QMessageBox QLabel {{
            color: #2c3e50;
            font-size: 14px;
            text-align: right;
        }}
    """)

    msg_box.exec_()

from datetime import datetime, date

class TransactionDialog(QDialog):
    def __init__(self, db_manager, current_user):
        super().__init__()
        self.db = db_manager
        self.current_user = current_user
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("💰 إضافة عملية مالية جديدة")
        self.setFixedSize(500, 400)

        # إعداد الاتجاه من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق أنماط RTL
        self.setStyleSheet("""
            QDialog {
                qproperty-layoutDirection: RightToLeft;
            }
            * {
                font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
                direction: rtl;
            }
            QLabel {
                text-align: right;
                qproperty-alignment: AlignRight;
            }
            QLineEdit {
                text-align: right;
                qproperty-alignment: AlignRight;
            }
            QTextEdit {
                text-align: right;
                qproperty-alignment: AlignRight;
            }
            QComboBox {
                text-align: right;
            }
        """)

        layout = QVBoxLayout()
        
        # معلومات العملية
        form_layout = QFormLayout()
        
        # نوع العملية
        self.transaction_type = QComboBox()
        self.transaction_type.addItems(["استلام", "تسليم"])
        form_layout.addRow("نوع العملية:", self.transaction_type)
        
        # العملة
        self.currency = QComboBox()
        self.currency.addItems(["دينار ليبي", "دولار أمريكي"])
        form_layout.addRow("العملة:", self.currency)
        
        # المبلغ
        self.amount = QDoubleSpinBox()
        self.amount.setMaximum(999999999.99)
        self.amount.setDecimals(2)
        self.amount.setSuffix(" ")
        form_layout.addRow("المبلغ:", self.amount)
        
        # سعر الصرف
        self.exchange_rate = QDoubleSpinBox()
        self.exchange_rate.setMaximum(999.99)
        self.exchange_rate.setDecimals(4)
        self.exchange_rate.setValue(1.0000)
        form_layout.addRow("سعر الصرف:", self.exchange_rate)
        
        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow("التاريخ:", self.date_edit)
        
        # اسم الجهة
        self.party_name = QLineEdit()
        self.party_name.setPlaceholderText("اسم الزبون أو الموقع أو الوسيط")
        form_layout.addRow("اسم الجهة:", self.party_name)
        
        # رقم المرجع
        self.reference_number = QLineEdit()
        self.reference_number.setPlaceholderText("سيتم إنشاؤه تلقائياً")
        self.reference_number.setReadOnly(True)
        self.reference_number.setStyleSheet("""
            QLineEdit {
                background-color: #f0f0f0;
                color: #666;
                border: 1px solid #ccc;
            }
        """)

        # زر إنشاء رقم مرجع جديد
        ref_layout = QHBoxLayout()
        ref_layout.addWidget(self.reference_number)

        self.generate_ref_btn = QPushButton("🔄 إنشاء رقم جديد")
        self.generate_ref_btn.setStyleSheet("""
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2980b9;
            }
        """)
        self.generate_ref_btn.clicked.connect(self.generate_reference_number)
        ref_layout.addWidget(self.generate_ref_btn)

        form_layout.addRow("رقم المرجع:", ref_layout)
        
        # الوصف
        self.description = QTextEdit()
        self.description.setMaximumHeight(80)
        self.description.setPlaceholderText("وصف إضافي للعملية")
        form_layout.addRow("الوصف:", self.description)
        
        layout.addLayout(form_layout)
        
        # أزرار التحكم
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.save_button.clicked.connect(self.save_transaction)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # ربط الأحداث
        self.currency.currentTextChanged.connect(self.update_exchange_rate)

        # ربط تغيير نوع العملية والعملة لإنشاء رقم مرجع جديد
        self.transaction_type.currentTextChanged.connect(self.generate_reference_number)
        self.currency.currentTextChanged.connect(self.generate_reference_number)

        # إنشاء رقم مرجع أولي
        self.generate_reference_number()

        # تركيز على حقل المبلغ
        self.amount.setFocus()
        
    def update_exchange_rate(self):
        """تحديث سعر الصرف عند تغيير العملة"""
        current_currency = self.currency.currentText()
        
        if current_currency == "دولار أمريكي":
            # الحصول على آخر سعر صرف من الدولار إلى الدينار
            rate = self.db.get_latest_exchange_rate("دولار أمريكي", "دينار ليبي")
            self.exchange_rate.setValue(rate)
        else:
            self.exchange_rate.setValue(1.0000)

    def generate_reference_number(self):
        """إنشاء رقم مرجع تلقائي"""
        try:
            transaction_type = self.transaction_type.currentText()
            currency = self.currency.currentText()

            # إنشاء رقم المرجع من قاعدة البيانات
            reference_number = self.db.generate_reference_number(transaction_type, currency)

            # تحديث حقل رقم المرجع
            self.reference_number.setText(reference_number)

        except Exception:
            # في حالة الخطأ، استخدم رقم مرجع بسيط
            from datetime import datetime
            simple_ref = f"REF-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            self.reference_number.setText(simple_ref)
    
    def save_transaction(self):
        """حفظ العملية المالية"""
        # التحقق من صحة البيانات
        if not self.validate_input():
            return
            
        try:
            # جمع البيانات
            transaction_type = self.transaction_type.currentText()
            currency = self.currency.currentText()
            amount = self.amount.value()
            exchange_rate = self.exchange_rate.value()
            transaction_date = self.date_edit.date().toString("yyyy-MM-dd")
            party_name = self.party_name.text().strip()
            reference_number = self.reference_number.text().strip()

            # التأكد من وجود رقم مرجع، وإنشاء واحد جديد إذا لم يكن موجوداً
            if not reference_number:
                self.generate_reference_number()
                reference_number = self.reference_number.text().strip()
            description = self.description.toPlainText().strip()
            user_id = self.current_user['id'] if self.current_user else None
            
            # حفظ العملية في قاعدة البيانات
            self.db.add_transaction(
                transaction_type, currency, amount, exchange_rate,
                transaction_date, party_name, reference_number,
                description, user_id
            )
            
            # رسالة نجاح
            create_arabic_info_box(self, "✅ نجح الحفظ", "تم حفظ العملية المالية بنجاح")

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            create_arabic_info_box(self, "❌ خطأ", f"حدث خطأ أثناء حفظ العملية:\n{str(e)}", QMessageBox.Critical)
    
    def validate_input(self):
        """التحقق من صحة البيانات المدخلة"""
        if self.amount.value() <= 0:
            create_arabic_info_box(self, "⚠️ خطأ في البيانات", "يجب أن يكون المبلغ أكبر من صفر", QMessageBox.Warning)
            self.amount.setFocus()
            return False

        if not self.party_name.text().strip():
            create_arabic_info_box(self, "⚠️ خطأ في البيانات", "يجب إدخال اسم الجهة", QMessageBox.Warning)
            self.party_name.setFocus()
            return False
            
        return True

class ExchangeRateDialog(QDialog):
    def __init__(self, db_manager):
        super().__init__()
        self.db = db_manager
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("💱 إدارة أسعار الصرف")
        self.setFixedSize(400, 300)

        # إعداد الاتجاه من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق أنماط RTL
        self.setStyleSheet("""
            QDialog {
                qproperty-layoutDirection: RightToLeft;
            }
            * {
                font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
                direction: rtl;
            }
            QLabel {
                text-align: right;
                qproperty-alignment: AlignRight;
            }
            QLineEdit {
                text-align: right;
                qproperty-alignment: AlignRight;
            }
            QComboBox {
                text-align: right;
            }
        """)

        layout = QVBoxLayout()
        
        # نموذج إضافة سعر صرف جديد
        form_group = QGroupBox("إضافة سعر صرف جديد")
        form_layout = QFormLayout()
        
        # من العملة
        self.from_currency = QComboBox()
        self.from_currency.addItems(["دولار أمريكي", "دينار ليبي"])
        form_layout.addRow("من العملة:", self.from_currency)
        
        # إلى العملة
        self.to_currency = QComboBox()
        self.to_currency.addItems(["دينار ليبي", "دولار أمريكي"])
        form_layout.addRow("إلى العملة:", self.to_currency)
        
        # السعر
        self.rate = QDoubleSpinBox()
        self.rate.setMaximum(999.9999)
        self.rate.setDecimals(4)
        self.rate.setValue(4.8000)  # سعر افتراضي
        form_layout.addRow("السعر:", self.rate)
        
        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow("التاريخ:", self.date_edit)
        
        form_group.setLayout(form_layout)
        layout.addWidget(form_group)
        
        # أزرار التحكم
        button_layout = QHBoxLayout()
        
        self.add_rate_button = QPushButton("إضافة السعر")
        self.add_rate_button.clicked.connect(self.add_exchange_rate)
        
        self.close_button = QPushButton("إغلاق")
        self.close_button.clicked.connect(self.accept)
        
        button_layout.addWidget(self.add_rate_button)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def add_exchange_rate(self):
        """إضافة سعر صرف جديد"""
        try:
            from_curr = self.from_currency.currentText()
            to_curr = self.to_currency.currentText()
            rate = self.rate.value()
            rate_date = self.date_edit.date().toString("yyyy-MM-dd")
            
            if from_curr == to_curr:
                create_arabic_info_box(self, "⚠️ خطأ", "لا يمكن أن تكون العملة المصدر والهدف متشابهتين", QMessageBox.Warning)
                return

            if rate <= 0:
                create_arabic_info_box(self, "⚠️ خطأ", "يجب أن يكون السعر أكبر من صفر", QMessageBox.Warning)
                return
            
            self.db.add_exchange_rate(from_curr, to_curr, rate, rate_date)
            
            create_arabic_info_box(self, "✅ نجح الحفظ", "تم حفظ سعر الصرف بنجاح")

            # إعادة تعيين القيم
            self.rate.setValue(4.8000)
            self.date_edit.setDate(QDate.currentDate())

        except Exception as e:
            create_arabic_info_box(self, "❌ خطأ", f"حدث خطأ أثناء حفظ سعر الصرف:\n{str(e)}", QMessageBox.Critical)

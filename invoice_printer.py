#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
طباعة الفواتير للعمليات المالية
"""

import os
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.units import inch, cm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from config import Config

class InvoicePrinter:
    def __init__(self, db_manager):
        self.db = db_manager
        self.setup_fonts()
        
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تسجيل خط عربي
            font_path = "assets/fonts/NotoSansArabic-Regular.ttf"
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('Arabic', font_path))
            else:
                # استخدام خط افتراضي
                pass
        except:
            pass
    
    def print_invoice(self, transaction_id, save_path=None):
        """طباعة فاتورة للعملية المالية"""
        try:
            # الحصول على بيانات العملية
            transaction = self.db.get_transaction_by_id(transaction_id)
            if not transaction:
                raise Exception("لم يتم العثور على العملية المطلوبة")
            
            # الحصول على معلومات الشركة
            company_info = Config.get_company_info()
            
            # إنشاء اسم الملف
            if not save_path:
                invoice_dir = "invoices"
                os.makedirs(invoice_dir, exist_ok=True)
                filename = f"invoice_{transaction[0]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
                save_path = os.path.join(invoice_dir, filename)
            
            # إنشاء المستند
            doc = SimpleDocTemplate(
                save_path,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            
            # إنشاء المحتوى
            story = []
            
            # إضافة رأس الفاتورة
            self._add_header(story, company_info, transaction)
            
            # إضافة تفاصيل العملية
            self._add_transaction_details(story, transaction)
            
            # إضافة ذيل الفاتورة
            self._add_footer(story, company_info)
            
            # بناء المستند
            doc.build(story)
            
            return save_path
            
        except Exception as e:
            raise Exception(f"خطأ في طباعة الفاتورة: {str(e)}")
    
    def _add_header(self, story, company_info, transaction):
        """إضافة رأس الفاتورة"""
        # إنشاء الأنماط
        styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        title_style = ParagraphStyle(
            'TitleArabic',
            parent=styles['Title'],
            fontSize=20,
            textColor=colors.HexColor('#2c3e50'),
            alignment=TA_CENTER,
            spaceAfter=20
        )
        
        # نمط معلومات الشركة
        company_style = ParagraphStyle(
            'CompanyArabic',
            parent=styles['Normal'],
            fontSize=12,
            textColor=colors.HexColor('#34495e'),
            alignment=TA_CENTER,
            spaceAfter=10
        )
        
        # عنوان الفاتورة
        invoice_type = "فاتورة استلام" if transaction[1] == "استلام" else "فاتورة تسليم"
        story.append(Paragraph(f"🧾 {invoice_type}", title_style))
        
        # معلومات الشركة
        story.append(Paragraph(f"<b>{company_info['name']}</b>", company_style))
        story.append(Paragraph(f"{company_info['name_en']}", company_style))
        story.append(Paragraph(f"📍 {company_info['address']}", company_style))
        story.append(Paragraph(f"📞 {company_info['phone']} | 📱 {company_info['mobile']}", company_style))
        story.append(Paragraph(f"✉️ {company_info['email']} | 🌐 {company_info['website']}", company_style))
        
        story.append(Spacer(1, 20))
        
        # معلومات الفاتورة
        invoice_info_style = ParagraphStyle(
            'InvoiceInfo',
            parent=styles['Normal'],
            fontSize=11,
            textColor=colors.HexColor('#2c3e50'),
            alignment=TA_RIGHT,
            spaceAfter=5
        )
        
        # جدول معلومات الفاتورة
        invoice_data = [
            ['رقم الفاتورة:', transaction[7] or f"INV-{transaction[0]}"],
            ['تاريخ الفاتورة:', transaction[5]],
            ['وقت الإصدار:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['رقم الترخيص:', company_info['license_no']],
            ['الرقم الضريبي:', company_info['tax_no']]
        ]
        
        invoice_table = Table(invoice_data, colWidths=[4*cm, 6*cm])
        invoice_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.HexColor('#7f8c8d')),
            ('TEXTCOLOR', (1, 0), (1, -1), colors.HexColor('#2c3e50')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#ecf0f1')),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f8f9fa')),
        ]))
        
        story.append(invoice_table)
        story.append(Spacer(1, 20))
    
    def _add_transaction_details(self, story, transaction):
        """إضافة تفاصيل العملية"""
        styles = getSampleStyleSheet()
        
        # نمط عنوان القسم
        section_style = ParagraphStyle(
            'SectionTitle',
            parent=styles['Heading2'],
            fontSize=16,
            textColor=colors.HexColor('#2c3e50'),
            alignment=TA_CENTER,
            spaceAfter=15,
            spaceBefore=10
        )
        
        story.append(Paragraph("📋 تفاصيل العملية المالية", section_style))
        
        # تحديد لون نوع العملية
        operation_color = colors.HexColor('#27ae60') if transaction[1] == "استلام" else colors.HexColor('#e74c3c')
        currency_symbol = "د.ل" if transaction[2] == "دينار ليبي" else "$"
        
        # بيانات العملية
        transaction_data = [
            ['البيان', 'القيمة'],
            ['نوع العملية', transaction[1]],
            ['العملة', transaction[2]],
            ['المبلغ', f"{transaction[3]:.2f} {currency_symbol}"],
            ['سعر الصرف', f"{transaction[4]:.4f}"],
            ['التاريخ', transaction[5]],
            ['اسم الجهة', transaction[6]],
            ['رقم المرجع', transaction[7] or "غير محدد"],
            ['الوصف', transaction[8] or "لا يوجد وصف"]
        ]
        
        # إنشاء الجدول
        transaction_table = Table(transaction_data, colWidths=[5*cm, 8*cm])
        transaction_table.setStyle(TableStyle([
            # رأس الجدول
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#34495e')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            
            # محتوى الجدول
            ('ALIGN', (0, 1), (-1, -1), 'RIGHT'),
            ('FONTSIZE', (0, 1), (-1, -1), 11),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#bdc3c7')),
            
            # تلوين الصفوف المتناوبة
            ('BACKGROUND', (0, 1), (-1, 1), colors.HexColor('#ecf0f1')),
            ('BACKGROUND', (0, 3), (-1, 3), colors.HexColor('#ecf0f1')),
            ('BACKGROUND', (0, 5), (-1, 5), colors.HexColor('#ecf0f1')),
            ('BACKGROUND', (0, 7), (-1, 7), colors.HexColor('#ecf0f1')),
            
            # تمييز نوع العملية
            ('TEXTCOLOR', (1, 1), (1, 1), operation_color),
            ('FONTNAME', (1, 1), (1, 1), 'Helvetica-Bold'),
            
            # تمييز المبلغ
            ('TEXTCOLOR', (1, 3), (1, 3), colors.HexColor('#2c3e50')),
            ('FONTNAME', (1, 3), (1, 3), 'Helvetica-Bold'),
            ('FONTSIZE', (1, 3), (1, 3), 14),
            
            # تمييز اسم الجهة
            ('FONTNAME', (1, 6), (1, 6), 'Helvetica-Bold'),
        ]))
        
        story.append(transaction_table)
        story.append(Spacer(1, 30))
    
    def _add_footer(self, story, company_info):
        """إضافة ذيل الفاتورة"""
        styles = getSampleStyleSheet()
        
        # نمط الذيل
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=10,
            textColor=colors.HexColor('#7f8c8d'),
            alignment=TA_CENTER,
            spaceAfter=5
        )
        
        # خط فاصل
        story.append(Spacer(1, 20))
        
        # رسالة شكر
        story.append(Paragraph("🙏 شكراً لتعاملكم معنا", footer_style))
        story.append(Paragraph("Thank you for your business", footer_style))
        
        story.append(Spacer(1, 10))
        
        # معلومات إضافية
        story.append(Paragraph(f"تم إصدار هذه الفاتورة بواسطة نظام {Config.APP_NAME}", footer_style))
        story.append(Paragraph(f"Generated by {Config.APP_NAME} System", footer_style))
        
        # تاريخ الطباعة
        print_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        story.append(Paragraph(f"تاريخ الطباعة: {print_time}", footer_style))
